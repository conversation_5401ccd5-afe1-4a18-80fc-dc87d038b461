codecov:
  require_ci_to_pass: yes

coverage:
  precision: 2
  round: down
  range: "60...90"
  status:
    project:
      default:
        target: 60%
        threshold: 5%
    patch:
      default:
        target: 50%
        threshold: 5%

parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no

comment:
  layout: "reach,diff,flags,files,footer"
  behavior: default
  require_changes: no
  show_carryforward_flags: true
  paths:
    - "src/main/"
  
github_checks:
  annotations: true
