@echo off
setlocal enabledelayedexpansion

REM KBuilder CLI - AI-Driven Coding Assistant (Windows)
REM This script provides a convenient way to run the KBuilder CLI with proper input handling

REM Get the directory where this script is located
set "SCRIPT_DIR=%~dp0"
set "SCRIPT_DIR=%SCRIPT_DIR:~0,-1%"

REM Function to check if Java is installed
where java >nul 2>nul
if %errorlevel% neq 0 (
    echo [91m❌ Java is not installed or not in PATH[0m
    echo [94mℹ️  Please install Java 11 or higher[0m
    exit /b 1
)

REM Function to build the project if needed
if not exist "%SCRIPT_DIR%\build\libs" (
    echo [94mℹ️  Building KBuilder CLI...[0m
    cd /d "%SCRIPT_DIR%"
    call gradlew.bat build -q
    if !errorlevel! neq 0 (
        echo [91m❌ Build failed[0m
        exit /b 1
    )
    echo [92m✅ Build completed successfully[0m
)

REM Function to run with <PERSON>radle (development mode)
if "%1"=="--dev" (
    echo [94mℹ️  Running in development mode with Gradle...[0m
    cd /d "%SCRIPT_DIR%"
    shift
    call gradlew.bat run --args="%*" --console=plain -q
    exit /b %errorlevel%
)

REM Function to show help
if "%1"=="--help" (
    echo KBuilder CLI - AI-Driven Coding Assistant
    echo.
    echo Usage:
    echo   kbuilder [COMMAND] [OPTIONS]
    echo   kbuilder --dev [COMMAND] [OPTIONS]    # Run in development mode
    echo   kbuilder --build                      # Force rebuild
    echo   kbuilder --help                       # Show this help
    echo.
    echo Examples:
    echo   kbuilder continuous "Create a User class"
    echo   kbuilder continuous "Create a User class" --debug
    echo   kbuilder ask "How to implement singleton in Kotlin?"
    echo   kbuilder config set openai.api_key sk-your-key
    echo   kbuilder test-connection
    echo.
    echo For detailed command help:
    echo   kbuilder --help
    echo.
    exit /b 0
)

REM Function to force rebuild
if "%1"=="--build" (
    echo [94mℹ️  Force rebuilding KBuilder CLI...[0m
    cd /d "%SCRIPT_DIR%"
    call gradlew.bat clean build
    echo [92m✅ Build completed[0m
    exit /b 0
)

REM Function to run with JAR (production mode)
for %%f in ("%SCRIPT_DIR%\build\libs\*.jar") do (
    if not "%%~nf"=="*-plain" (
        set "JAR_FILE=%%f"
        goto :found_jar
    )
)

echo [91m❌ JAR file not found. Please run: gradlew.bat build[0m
exit /b 1

:found_jar
echo [94mℹ️  Running KBuilder CLI...[0m
java -jar "%JAR_FILE%" %*
exit /b %errorlevel%
