#!/bin/bash

# KBuilder CLI - AI-Driven Coding Assistant
# This script provides a convenient way to run the KBuilder CLI with proper input handling

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if Java is installed
check_java() {
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        print_info "Please install Java 11 or higher"
        exit 1
    fi
    
    # Check Java version
    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$java_version" -lt 11 ]; then
        print_error "Java 11 or higher is required (found Java $java_version)"
        exit 1
    fi
}

# Function to build the project if needed
build_if_needed() {
    if [ ! -d "$SCRIPT_DIR/build/libs" ] || [ ! -f "$SCRIPT_DIR/build/libs"/*.jar ]; then
        print_info "Building KBuilder CLI..."
        cd "$SCRIPT_DIR"
        if ./gradlew build -q; then
            print_success "Build completed successfully"
        else
            print_error "Build failed"
            exit 1
        fi
    fi
}

# Function to run with Gradle (development mode)
run_with_gradle() {
    print_info "Running in development mode with Gradle..."
    cd "$SCRIPT_DIR"
    exec ./gradlew run --args="$*" --console=plain -q
}

# Function to run with JAR (production mode)
run_with_jar() {
    local jar_file=$(find "$SCRIPT_DIR/build/libs" -name "*.jar" -not -name "*-plain.jar" | head -n 1)
    if [ -z "$jar_file" ]; then
        print_error "JAR file not found. Please run: ./gradlew build"
        exit 1
    fi
    
    print_info "Running KBuilder CLI..."
    exec java -jar "$jar_file" "$@"
}

# Function to show help
show_help() {
    echo "KBuilder CLI - AI-Driven Coding Assistant"
    echo ""
    echo "Usage:"
    echo "  ./kbuilder [COMMAND] [OPTIONS]"
    echo "  ./kbuilder --dev [COMMAND] [OPTIONS]    # Run in development mode"
    echo "  ./kbuilder --build                      # Force rebuild"
    echo "  ./kbuilder --help                       # Show this help"
    echo ""
    echo "Examples:"
    echo "  ./kbuilder continuous \"Create a User class\""
    echo "  ./kbuilder continuous \"Create a User class\" --debug"
    echo "  ./kbuilder ask \"How to implement singleton in Kotlin?\""
    echo "  ./kbuilder config set openai.api_key sk-your-key"
    echo "  ./kbuilder test-connection"
    echo ""
    echo "For detailed command help:"
    echo "  ./kbuilder --help"
    echo ""
}

# Main execution
main() {
    # Check for special flags
    case "$1" in
        --help|-h)
            show_help
            exit 0
            ;;
        --dev)
            shift
            check_java
            run_with_gradle "$@"
            ;;
        --build)
            print_info "Force rebuilding KBuilder CLI..."
            cd "$SCRIPT_DIR"
            ./gradlew clean build
            print_success "Build completed"
            exit 0
            ;;
        *)
            check_java
            build_if_needed
            run_with_jar "$@"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
