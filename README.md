# AI Coding CLI (KBuilderTDD)

[![Kotlin CI with Gradle](https://github.com/iptton-ai/kbuildercli/actions/workflows/unit-tests.yml/badge.svg)](https://github.com/iptton-ai/kbuildercli/actions/workflows/unit-tests.yml)
[![Code Coverage](https://img.shields.io/badge/Code%20Coverage-60.18%25-success.svg)](https://github.com/iptton-ai/KBuilderTDD/actions/workflows/unit-tests.yml)
[![Branch Coverage](https://img.shields.io/badge/Branch%20Coverage-40.50%25-yellow.svg)](https://github.com/iptton-ai/KBuilderTDD/actions/workflows/unit-tests.yml)

一个功能强大的 AI 辅助编程命令行工具，使用 TDD（测试驱动开发）方法构建。支持多种 AI 服务提供商，提供代码分析、对话历史管理、插件系统等丰富功能。

## 🚀 主要功能

### 🤖 AI 服务集成
- **多提供商支持**：OpenAI、Claude、Ollama（本地）
- **智能对话**：支持上下文对话和历史管理
- **流式响应**：实时显示 AI 回复
- **模型选择**：灵活切换不同 AI 模型

### � 连续对话与自动执行
- **自动任务分解**：将复杂需求自动分解为可执行任务
- **工具自动调用**：AI 自动选择和调用合适的工具
- **25轮执行限制**：安全的自动执行机制，防止无限循环
- **会话状态管理**：支持暂停、继续和恢复执行
- **智能工具选择**：根据需求自动选择文件操作、代码分析等工具

### �📊 代码分析
- **文件分析**：单文件代码质量分析
- **项目分析**：整个项目的代码度量
- **代码度量**：复杂度、可维护性指数、重复代码检测
- **问题检测**：代码质量问题识别和改进建议

### 🔧 插件系统
- **插件管理**：安装、卸载、启用、禁用插件
- **命令扩展**：通过插件添加自定义命令
- **AI 服务扩展**：支持第三方 AI 服务集成
- **安全沙箱**：插件权限控制和安全机制

### 📝 对话历史
- **历史管理**：保存、查看、搜索对话记录
- **上下文继续**：继续之前的对话
- **智能搜索**：基于内容搜索历史对话
- **统计分析**：对话使用统计和分析

### ⚙️ 配置管理
- **多服务配置**：管理多个 AI 服务的配置
- **安全存储**：API 密钥安全存储和显示
- **默认设置**：设置默认提供商和模型
- **配置验证**：自动验证配置有效性

## 📦 安装和构建

### 系统要求
- Java 11 或更高版本
- Gradle 7.0 或更高版本

### 快速安装
```bash
# 克隆项目
git clone https://github.com/iptton-ai/kbuildercli.git
cd kbuildercli

# 使脚本可执行 (Linux/macOS)
chmod +x kbuilder

# 运行应用
./kbuilder --help          # Linux/macOS
kbuilder.bat --help        # Windows
```

### 🎯 **重要：用户输入支持**

**对于需要用户交互的命令（如带有AI驱动提示的 `continuous` 命令），您必须使用提供的脚本：**

✅ **正确方式（支持用户输入）：**
```bash
# Linux/macOS
./kbuilder continuous "Create a User class" --debug

# Windows
kbuilder.bat continuous "Create a User class" --debug
```

❌ **错误方式（不支持用户输入）：**
```bash
# 这种方式对交互式命令无效
./gradlew run --args="continuous 'Create a User class'"
```

**原因：** Gradle 的 `run` 任务默认不转发标准输入，因此当AI请求澄清时，CLI无法接收用户输入。

### 开发者构建
```bash
# 构建项目
./gradlew build

# 开发模式运行（支持用户输入）
./kbuilder --dev --help

# 传统方式（仅用于非交互式命令）
./gradlew run --args="--help"
```

### 创建可执行文件
```bash
# 创建分发包
./gradlew distZip

# 解压并使用
unzip build/distributions/ai-coding-cli-0.1.0.zip
cd ai-coding-cli-0.1.0/bin
./ai-coding-cli --help
```

## 🎯 快速开始

### 1. 配置 AI 服务
```bash
# 配置 OpenAI
./kbuilder config set openai.api_key sk-your-openai-key

# 配置 Claude
./kbuilder config set claude.api_key sk-ant-your-claude-key

# 设置默认提供商
./kbuilder config provider openai

# 查看配置
./kbuilder config list
```

### 2. 测试连接
```bash
# 测试默认提供商连接
./kbuilder test-connection

# 测试特定提供商
./kbuilder test-connection --provider claude

# 测试本地 Ollama（无需 API 密钥）
./kbuilder test-connection --provider ollama --model llama2
```

### 3. 开始对话
```bash
# 基础问答
./kbuilder ask "Hello, how are you?"

# 使用流式响应
./kbuilder ask "Explain Kotlin coroutines" --stream

# 指定提供商和模型
./kbuilder ask "Write a Kotlin function" --provider claude --model claude-3-sonnet-20240229

# 继续之前的对话
./kbuilder ask "Can you elaborate?" --continue abc123

# 🚀 AI驱动的自动执行（支持用户交互）
./kbuilder continuous "Create a User class with validation" --debug
```

## 📖 详细使用指南

### AI 对话功能

#### 基础对话
```bash
# 简单问答
ai-coding-cli ask "What is Kotlin?"

# 编程相关问题
ai-coding-cli ask "How to implement a singleton in Kotlin?"

# 代码审查
ai-coding-cli ask "Review this code: fun main() { println(\"Hello\") }"
```

#### 连续对话与自动执行
```bash
# 自动执行复杂任务（实验性功能）
ai-coding-cli continuous "Create a User data class with validation"

# 自动创建完整的 REST API
ai-coding-cli continuous "Create a REST API for user management with CRUD operations"

# 继续之前暂停的自动执行会话
ai-coding-cli continuous --continue <session-id>

# 查看自动执行会话状态
ai-coding-cli continuous --status <session-id>

# 列出所有自动执行会话
ai-coding-cli continuous --list
```

**连续对话功能说明：**
- 🤖 **智能任务分解**：AI 自动将复杂需求分解为多个可执行的子任务
- 🔧 **自动工具调用**：根据任务需要自动调用文件操作、代码分析等工具
- 🛡️ **安全限制**：最多执行 25 轮，防止无限循环
- ⏸️ **可暂停恢复**：支持暂停执行并稍后继续
- 📊 **执行跟踪**：详细记录每个执行步骤和结果

**支持的任务类型：**
- 📝 **简单类创建**：自动生成 Kotlin 类文件
- 🌐 **REST API 开发**：创建完整的 Controller、Service、Model 结构
- ⚙️ **配置文件生成**：生成应用配置文件
- 📊 **数据模型创建**：生成数据类和实体类

#### 高级对话选项
```bash
# 流式响应（实时显示）
ai-coding-cli ask "Explain design patterns" --stream

# 指定 AI 提供商
ai-coding-cli ask "Write unit tests" --provider openai
ai-coding-cli ask "Code review" --provider claude
ai-coding-cli ask "Local inference" --provider ollama --model codellama

# 指定模型
ai-coding-cli ask "Complex algorithm" --model gpt-4
ai-coding-cli ask "Quick question" --model gpt-3.5-turbo

# 继续对话
ai-coding-cli ask "Follow up question" --continue abc123

# 强制新对话
ai-coding-cli ask "New topic" --new
```

### 对话历史管理

#### 查看历史
```bash
# 列出最近对话
ai-coding-cli history list

# 限制显示数量
ai-coding-cli history list --limit 5

# 查看特定对话详情
ai-coding-cli history show abc123

# 搜索对话
ai-coding-cli history search "kotlin"
ai-coding-cli history search "unit test"
```

#### 管理历史
```bash
# 删除特定对话
ai-coding-cli history delete abc123

# 清空所有历史
ai-coding-cli history clear

# 查看统计信息
ai-coding-cli history stats
```

### 代码分析功能

#### 文件分析
```bash
# 分析单个文件
ai-coding-cli analyze file src/main/kotlin/Main.kt

# 指定输出格式
ai-coding-cli analyze file Main.kt --format json

# 强制指定语言
ai-coding-cli analyze file script.txt --language kotlin
```

#### 项目分析
```bash
# 分析整个项目
ai-coding-cli analyze project src/main/kotlin

# 分析当前目录
ai-coding-cli analyze project .
```

#### 专项分析
```bash
# 代码度量
ai-coding-cli analyze metrics src/main/kotlin/Main.kt
ai-coding-cli analyze metrics Main.kt --format json

# 问题检测
ai-coding-cli analyze issues src/main/kotlin/Main.kt
ai-coding-cli analyze issues Main.kt --format json
```

### 插件系统

#### 插件管理
```bash
# 列出所有插件
ai-coding-cli plugin list

# 安装插件
ai-coding-cli plugin install ./my-plugin.jar
ai-coding-cli plugin install https://example.com/plugin.jar

# 卸载插件
ai-coding-cli plugin uninstall my-plugin-id

# 启用/禁用插件
ai-coding-cli plugin enable my-plugin-id
ai-coding-cli plugin disable my-plugin-id
```

#### 插件信息
```bash
# 查看插件详情
ai-coding-cli plugin info my-plugin-id

# 验证插件
ai-coding-cli plugin validate ./my-plugin.jar
```

### 配置管理

#### 基础配置
```bash
# 设置 API 密钥
ai-coding-cli config set openai.api_key sk-your-key
ai-coding-cli config set claude.api_key sk-ant-your-key

# 设置默认提供商
ai-coding-cli config provider openai
ai-coding-cli config provider claude
ai-coding-cli config provider ollama

# 查看配置
ai-coding-cli config get openai.api_key
ai-coding-cli config list
```

#### 高级配置
```bash
# 设置默认模型
ai-coding-cli config set openai.default_model gpt-4
ai-coding-cli config set claude.default_model claude-3-sonnet-20240229

# 设置 Ollama 服务地址
ai-coding-cli config set ollama.base_url http://localhost:11434
```

## 持续集成

本项目使用 GitHub Actions 进行持续集成，自动执行以下任务：

- 在每次代码推送或 Pull Request 时构建项目
- 执行所有单元测试
- 生成 JaCoCo 测试覆盖率报告
- 验证覆盖率是否满足最低要求（50%）
- 将测试结果和覆盖率报告作为构建产物保存

同时，我们还配置了智能 Issue 分析功能：

- 使用 AutoDev Remote Agent 自动分析新建和更新的 Issues
- 基于 DeepSeek API 提供代码上下文智能分析
- 自动添加相关标签并发表分析评论
- 提供详细的代码文件分析和建议

### 测试覆盖率

当前项目的测试覆盖率状况：

- 指令（代码行）覆盖率：60.18%
- 分支覆盖率：40.50%

## 如何运行测试

本地执行测试和生成覆盖率报告：

```bash
# 运行测试
./gradlew test

# 生成覆盖率报告
./gradlew jacocoTestReport

# 验证覆盖率是否达到要求
./gradlew jacocoTestCoverageVerification
```

覆盖率报告将生成在 `build/jacocoHtml/` 目录下。

## CI 配置指南

### GitHub Actions 设置

1. **设置必要的 API Token**

   项目 CI 需要配置以下 API Token：

   - **DeepSeek API Token**（用于 Issue 智能分析）：
     - 访问 GitHub 仓库的 "Settings" > "Secrets and variables" > "Actions"
     - 点击 "New repository secret"
     - 名称设置为：`DEEPSEEK_TOKEN`
     - 值设置为你的 DeepSeek API 令牌
     - 点击 "Add secret" 保存
     
   - **Codecov Token**（用于覆盖率报告发布）：
     - 访问 [Codecov](https://codecov.io/) 并注册/登录
     - 关联你的 GitHub 仓库
     - 获取 Codecov token
     - 在 GitHub 仓库添加名为 `CODECOV_TOKEN` 的仓库密钥

2. **工作流文件**

   本项目包含两个主要的 GitHub Actions 工作流：
   - `unit-tests.yml` - 用于代码构建和测试
   - `advanced-issue-analysis.yml` - 用于 Issue 智能分析

   这些工作流大部分情况下会自动运行，也支持手动触发。

3. **Issue 智能分析**

   项目使用 AutoDev Remote Agent 提供 Issue 智能分析功能：
   
   - **自动分析**：新建或更新 Issue 时会自动触发分析
   - **手动分析**：在 Actions 页面选择 "Advanced Issue Analysis" 工作流，输入 Issue 编号
   - **分析深度**：可选择 shallow（浅）、medium（中，默认）、deep（深）三种深度
   
   分析结果将作为评论添加到 Issue 中，包含以下内容：

   - 问题分析和建议
   - 相关代码引用
   - 处理步骤指导
   - 文件过滤和分析过程信息

## 🏗️ 技术架构

### 核心组件

#### AI 服务抽象层
- **AiServiceFactory**: 统一的 AI 服务工厂
- **AiService**: 抽象接口，支持多种 AI 提供商
- **流式响应**: 支持 SSE 和 JSONL 格式的实时响应

#### 配置管理
- **ConfigManager**: JSON 配置文件管理
- **多提供商配置**: 支持 OpenAI、Claude、Ollama
- **安全存储**: API 密钥加密存储

#### 对话历史
- **HistoryManager**: 对话历史管理
- **ConversationSession**: 对话会话模型
- **智能上下文**: 自动检测兼容对话并继续

#### 代码分析
- **CodeAnalyzer**: 代码质量分析引擎
- **语言检测**: 自动识别编程语言
- **度量计算**: 复杂度、可维护性、重复代码检测

#### 插件系统
- **PluginManager**: 插件生命周期管理
- **安全沙箱**: 插件权限控制
- **动态加载**: 运行时插件加载和卸载

#### 连续对话系统
- **AutoExecutionEngine**: 自动执行引擎，支持任务分解和工具调用
- **TaskDecomposer**: 需求分解器，将复杂需求分解为可执行任务
- **ToolExecutor**: 工具执行器，支持多种工具的自动调用
- **ConversationStateManager**: 会话状态管理，支持暂停和恢复
- **AiPromptEngine**: AI 提示引擎，智能决策下一步操作

### 支持的 AI 提供商

| 提供商 | 模型示例 | 特性 |
|--------|----------|------|
| **OpenAI** | gpt-4, gpt-3.5-turbo | 强大的通用能力，支持流式响应 |
| **Claude** | claude-3-sonnet-20240229 | 优秀的代码理解和生成能力 |
| **Ollama** | llama2, codellama | 本地部署，无需 API 密钥 |

### 支持的自动执行工具

| 工具类型 | 工具名称 | 功能描述 |
|---------|----------|----------|
| **文件操作** | save-file | 创建和保存文件 |
| | view | 查看文件内容和目录结构 |
| | str-replace-editor | 编辑文件内容 |
| | remove-files | 删除文件 |
| **代码分析** | codebase-retrieval | 智能代码检索和理解 |
| | analyze | 代码质量分析 |
| **任务管理** | task-management | 任务分解和管理 |
| **项目操作** | project-setup | 项目初始化和配置 |

### 数据存储

```
~/.ai-coding-cli/
├── config.json          # 配置文件
├── history/             # 对话历史
│   ├── conversations.json
│   └── sessions/
├── conversations/       # 连续对话会话
│   ├── sessions.json    # 会话状态存储
│   └── execution/       # 执行历史
└── plugins/             # 插件目录
    ├── installed/
    └── cache/
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
./gradlew test

# 运行特定测试类
./gradlew test --tests "AiCodingCliTest"

# 运行测试并生成覆盖率报告
./gradlew test jacocoTestReport
```

### 测试覆盖率
- **当前覆盖率**: 60.18% (指令覆盖率)
- **分支覆盖率**: 40.50%
- **测试数量**: 164+ 个单元测试和集成测试

### 测试策略
- **TDD 开发**: 所有新功能都采用测试驱动开发
- **单元测试**: 每个组件都有完整的单元测试
- **集成测试**: 端到端功能测试
- **Mock 测试**: 网络调用和外部依赖的模拟测试

## 🔧 开发指南

### 环境要求
- **Java**: 11 或更高版本
- **Kotlin**: 2.0.0
- **Gradle**: 7.0+

### 开发设置
```bash
# 克隆项目
git clone https://github.com/iptton-ai/kbuildercli.git
cd kbuildercli

# 安装依赖
./gradlew build

# 运行开发版本
./gradlew run --args="--help"

# 启动测试监听
./gradlew test --continuous
```

### 代码规范
- **Kotlin 编码规范**: 遵循官方 Kotlin 编码规范
- **命名约定**: 使用有意义的变量和函数名
- **文档注释**: 公共 API 必须有 KDoc 注释
- **测试覆盖**: 新功能必须有对应的测试

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加新的 AI 提供商支持"

# 问题修复
git commit -m "fix: 修复配置文件加载问题"

# 测试相关
git commit -m "test: 添加历史管理单元测试"

# 文档更新
git commit -m "docs: 更新 README 使用说明"
```

## 🤝 贡献指南

### 如何贡献
1. **Fork** 项目到你的 GitHub 账户
2. **创建分支** 用于你的功能开发
3. **编写代码** 并确保通过所有测试
4. **提交 PR** 并描述你的更改

### 贡献类型
- 🐛 **Bug 修复**: 修复现有功能的问题
- ✨ **新功能**: 添加新的功能特性
- 📚 **文档**: 改进文档和示例
- 🧪 **测试**: 增加测试覆盖率
- 🔧 **工具**: 改进开发工具和流程

### 开发流程
1. **创建 Issue**: 描述问题或功能需求
2. **TDD 开发**: 先写测试，再实现功能
3. **代码审查**: 确保代码质量和规范
4. **CI 验证**: 通过所有自动化测试
5. **合并代码**: 经过审查后合并到主分支

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Kotlin](https://kotlinlang.org/) - 优秀的编程语言
- [Ktor](https://ktor.io/) - 强大的 HTTP 客户端
- [JUnit 5](https://junit.org/junit5/) - 现代化的测试框架
- [MockK](https://mockk.io/) - Kotlin 专用的 Mock 框架

## 📞 联系方式

- **GitHub Issues**: [提交问题和建议](https://github.com/iptton-ai/kbuildercli/issues)
- **项目主页**: https://github.com/iptton-ai/kbuildercli
- **文档**: 查看项目 Wiki 获取更多详细信息

---

**AI Coding CLI** - 让 AI 成为你的编程助手 🚀
