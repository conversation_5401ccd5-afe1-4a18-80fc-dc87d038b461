# 基础版 Issue 分析，仅在未配置高级分析时使用
# 默认禁用此工作流，使用 advanced-issue-analysis.yml 替代
name: Basic Issue Analysis

on:
  # 禁用自动触发
  # issues:
  #   types: [opened, edited, reopened]
  
  # 仅允许手动触发
  workflow_dispatch:
    inputs:
      issue_number:
        description: 'Issue 编号'
        required: true
        type: number

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整历史记录以便进行更好的分析

      - name: Analyze Issue with AutoDev Agent
        uses: unit-mesh/autodev-remote-agent-action@v0.4.0
        with:
          github-token: ${{ secrets.ISSUE_TOKEN }}
          deepseek-token: ${{ secrets.DEEPSEEK_TOKEN }}
          analysis-depth: medium
          auto-comment: true
          auto-label: true
          include-config-files: true
          include-test-files: true
