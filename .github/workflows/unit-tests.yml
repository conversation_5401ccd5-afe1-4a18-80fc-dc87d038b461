name: <PERSON><PERSON><PERSON> with <PERSON>rad<PERSON>

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 11
      uses: actions/setup-java@v4
      with:
        java-version: '11'
        distribution: 'temurin'
        
    - name: Cache Gradle dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
        
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Build with Gradle
      run: ./gradlew build
      
    - name: Run tests with JaCoCo
      run: ./gradlew test jacocoTestReport
      
    - name: Upload test report
      uses: actions/upload-artifact@v4
      with:
        name: test-report
        path: build/reports/tests/test/
        
    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: build/jacocoHtml/
        
    - name: Coverage verification
      run: ./gradlew jacocoTestCoverageVerification
      
    - name: Publish Test Report
      uses: mikepenz/action-junit-report@v4
      if: always() # 总是运行此步骤，即使前面的步骤失败
      with:
        report_paths: '**/build/test-results/test/TEST-*.xml'
        
    - name: Publish Coverage Report
      uses: codecov/codecov-action@v5
      with:
        file: build/reports/jacoco/test/jacocoTestReport.xml
        fail_ci_if_error: false
        token: ${{ secrets.CODECOV_TOKEN }}
        
    - name: Check Test Failures
      if: ${{ failure() }}
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // 查找失败的测试
          const testResultsDir = path.join(process.env.GITHUB_WORKSPACE, 'build/test-results/test');
          const files = fs.readdirSync(testResultsDir).filter(f => f.startsWith('TEST-') && f.endsWith('.xml'));
          
          let failedTests = [];
          for (const file of files) {
            const content = fs.readFileSync(path.join(testResultsDir, file), 'utf8');
            if (content.includes('failure') || content.includes('error')) {
              const match = file.match(/TEST-(.*).xml/);
              if (match) failedTests.push(match[1]);
            }
          }
          
          if (failedTests.length > 0) {
            const summary = `❌ ${failedTests.length} 个测试失败:\n${failedTests.join('\n')}`;
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            }).catch(e => console.error('无法发表评论:', e));
          }
