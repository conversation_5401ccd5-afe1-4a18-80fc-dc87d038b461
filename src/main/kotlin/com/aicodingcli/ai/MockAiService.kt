package com.aicodingcli.ai

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
 * Mock AI service for testing and demonstration purposes
 */
class MockAiService(override val config: AiServiceConfig) : AiService {
    
    override suspend fun chat(request: AiRequest): AiResponse {
        validateRequest(request)

        val userMessage = request.messages.lastOrNull { it.role == MessageRole.USER }?.content ?: ""
        val systemMessage = request.messages.firstOrNull { it.role == MessageRole.SYSTEM }?.content ?: ""

        // Handle conversation-based requests differently
        val mockResponse = if (systemMessage.isNotEmpty() && userMessage.contains("what should be the next action")) {
            handleConversationActionDecision(systemMessage, userMessage)
        } else {
            generateMockResponse(userMessage)
        }

        return AiResponse(
            content = mockResponse,
            model = request.model,
            usage = TokenUsage(
                promptTokens = (userMessage.length + systemMessage.length) / 4, // Rough estimate
                completionTokens = mockResponse.length / 4,
                totalTokens = (userMessage.length + systemMessage.length + mockResponse.length) / 4
            ),
            finishReason = FinishReason.STOP
        )
    }
    
    override suspend fun streamChat(request: AiRequest): Flow<AiStreamChunk> {
        val response = chat(request)
        return flowOf(
            AiStreamChunk(
                content = response.content,
                finishReason = response.finishReason
            )
        )
    }
    
    override suspend fun testConnection(): Boolean {
        return true // Mock always succeeds
    }
    
    private fun validateRequest(request: AiRequest) {
        require(request.messages.isNotEmpty()) { "Messages cannot be empty" }
        require(request.model.isNotBlank()) { "Model cannot be empty" }
    }
    
    private fun generateMockResponse(userMessage: String): String {
        return when {
            // AI Prompt Engine requests
            userMessage.contains("decide the next action") || userMessage.contains("next action to take") -> {
                generateActionDecisionResponse(userMessage)
            }
            userMessage.contains("analyze") && userMessage.contains("requirement") -> {
                generateAnalysisResponse(userMessage)
            }
            userMessage.contains("evaluate") && userMessage.contains("completed") -> {
                generateCompletionEvaluationResponse(userMessage)
            }
            // Task Decomposer requests
            userMessage.contains("decompose") || userMessage.contains("tasks") -> {
                generateTaskDecompositionResponse(userMessage)
            }
            userMessage.contains("refine") -> {
                generateTaskRefinementResponse(userMessage)
            }
            else -> {
                "This is a mock AI response for: $userMessage"
            }
        }
    }
    
    private fun generateTaskDecompositionResponse(userMessage: String): String {
        return when {
            userMessage.contains("Calculator") -> {
                """
                {
                    "analysis": {
                        "intent": "Create a Calculator class with basic arithmetic operations",
                        "complexity": "LOW",
                        "estimatedTasks": "1",
                        "requiredTools": ["save-file"]
                    },
                    "tasks": [
                        {
                            "id": "calculator-class-task",
                            "description": "Create Calculator class with add, subtract, multiply, and divide methods",
                            "toolCalls": [
                                {
                                    "toolName": "save-file",
                                    "parameters": {
                                        "path": "Calculator.kt",
                                        "file_content": "class Calculator {\n    fun add(a: Double, b: Double): Double = a + b\n    \n    fun subtract(a: Double, b: Double): Double = a - b\n    \n    fun multiply(a: Double, b: Double): Double = a * b\n    \n    fun divide(a: Double, b: Double): Double {\n        require(b != 0.0) { \"Division by zero is not allowed\" }\n        return a / b\n    }\n}"
                                    }
                                }
                            ],
                            "dependencies": [],
                            "priority": 1,
                            "estimatedDuration": "PT5M"
                        }
                    ]
                }
                """.trimIndent()
            }
            userMessage.contains("User") && userMessage.contains("class") -> {
                """
                {
                    "analysis": {
                        "intent": "Create a User data class with properties",
                        "complexity": "LOW",
                        "estimatedTasks": "1",
                        "requiredTools": ["save-file"]
                    },
                    "tasks": [
                        {
                            "id": "user-class-task",
                            "description": "Create User data class with name and email properties",
                            "toolCalls": [
                                {
                                    "toolName": "save-file",
                                    "parameters": {
                                        "path": "User.kt",
                                        "file_content": "data class User(\n    val id: Long,\n    val name: String,\n    val email: String\n) {\n    init {\n        require(name.isNotBlank()) { \"Name cannot be blank\" }\n        require(email.contains(\"@\")) { \"Invalid email format\" }\n    }\n}"
                                    }
                                }
                            ],
                            "dependencies": [],
                            "priority": 1,
                            "estimatedDuration": "PT3M"
                        }
                    ]
                }
                """.trimIndent()
            }
            userMessage.contains("REST API") || userMessage.contains("API") -> {
                """
                {
                    "analysis": {
                        "intent": "Create a complete REST API with model, service, and controller",
                        "complexity": "MEDIUM",
                        "estimatedTasks": "3",
                        "requiredTools": ["save-file"]
                    },
                    "tasks": [
                        {
                            "id": "user-model-task",
                            "description": "Create User data model",
                            "toolCalls": [
                                {
                                    "toolName": "save-file",
                                    "parameters": {
                                        "path": "User.kt",
                                        "file_content": "import javax.persistence.*\n\n@Entity\n@Table(name = \"users\")\ndata class User(\n    @Id\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\n    val id: Long = 0,\n    \n    @Column(nullable = false)\n    val name: String,\n    \n    @Column(nullable = false, unique = true)\n    val email: String\n)"
                                    }
                                }
                            ],
                            "dependencies": [],
                            "priority": 1,
                            "estimatedDuration": "PT5M"
                        },
                        {
                            "id": "user-service-task",
                            "description": "Create UserService for business logic",
                            "toolCalls": [
                                {
                                    "toolName": "save-file",
                                    "parameters": {
                                        "path": "UserService.kt",
                                        "file_content": "import org.springframework.stereotype.Service\n\n@Service\nclass UserService {\n    \n    fun getAllUsers(): List<User> {\n        // TODO: Implement database access\n        return emptyList()\n    }\n    \n    fun createUser(user: User): User {\n        // TODO: Implement user creation\n        return user\n    }\n    \n    fun getUserById(id: Long): User? {\n        // TODO: Implement user retrieval\n        return null\n    }\n    \n    fun updateUser(id: Long, user: User): User {\n        // TODO: Implement user update\n        return user\n    }\n    \n    fun deleteUser(id: Long) {\n        // TODO: Implement user deletion\n    }\n}"
                                    }
                                }
                            ],
                            "dependencies": ["user-model-task"],
                            "priority": 2,
                            "estimatedDuration": "PT10M"
                        },
                        {
                            "id": "user-controller-task",
                            "description": "Create UserController with REST endpoints",
                            "toolCalls": [
                                {
                                    "toolName": "save-file",
                                    "parameters": {
                                        "path": "UserController.kt",
                                        "file_content": "import org.springframework.web.bind.annotation.*\n\n@RestController\n@RequestMapping(\"/api/users\")\nclass UserController(private val userService: UserService) {\n    \n    @GetMapping\n    fun getAllUsers(): List<User> = userService.getAllUsers()\n    \n    @PostMapping\n    fun createUser(@RequestBody user: User): User = userService.createUser(user)\n    \n    @GetMapping(\"/{id}\")\n    fun getUserById(@PathVariable id: Long): User? = userService.getUserById(id)\n    \n    @PutMapping(\"/{id}\")\n    fun updateUser(@PathVariable id: Long, @RequestBody user: User): User = \n        userService.updateUser(id, user)\n    \n    @DeleteMapping(\"/{id}\")\n    fun deleteUser(@PathVariable id: Long) = userService.deleteUser(id)\n}"
                                    }
                                }
                            ],
                            "dependencies": ["user-service-task"],
                            "priority": 3,
                            "estimatedDuration": "PT8M"
                        }
                    ]
                }
                """.trimIndent()
            }
            else -> {
                """
                {
                    "analysis": {
                        "intent": "Generic implementation task",
                        "complexity": "LOW",
                        "estimatedTasks": "1",
                        "requiredTools": ["save-file"]
                    },
                    "tasks": [
                        {
                            "id": "generic-task",
                            "description": "Implement the requested functionality",
                            "toolCalls": [
                                {
                                    "toolName": "save-file",
                                    "parameters": {
                                        "path": "Implementation.kt",
                                        "file_content": "// Implementation for: ${userMessage.substringAfter("Requirement**: ").substringBefore("\\n")}\n// TODO: Add specific implementation based on requirements"
                                    }
                                }
                            ],
                            "dependencies": [],
                            "priority": 1,
                            "estimatedDuration": "PT5M"
                        }
                    ]
                }
                """.trimIndent()
            }
        }
    }
    
    private fun generateTaskRefinementResponse(userMessage: String): String {
        return """
        {
            "refinedTask": {
                "id": "refined-task",
                "description": "Refined task based on feedback",
                "toolCalls": [
                    {
                        "toolName": "save-file",
                        "parameters": {
                            "path": "RefinedImplementation.kt",
                            "file_content": "// Refined implementation based on feedback"
                        }
                    }
                ],
                "dependencies": [],
                "priority": 1
            }
        }
        """.trimIndent()
    }

    private fun generateActionDecisionResponse(userMessage: String): String {
        // Check if there's execution history indicating work has been done
        val hasExecutionHistory = userMessage.contains("save-file: true") ||
                                 userMessage.contains("Execution History") && !userMessage.contains("No previous actions taken")

        return when {
            userMessage.contains("Hello World") || userMessage.contains("hello world") -> {
                if (hasExecutionHistory) {
                    // Hello World function has been created, complete the task
                    """
                    {
                        "action": "COMPLETE",
                        "reasoning": "Hello World function has been successfully created",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                } else {
                    // Create the Hello World function
                    """
                    {
                        "action": "EXECUTE_TOOL",
                        "toolName": "save-file",
                        "parameters": {
                            "path": "HelloWorld.kt",
                            "file_content": "fun main() {\n    println(\"Hello, World!\")\n}\n\nfun helloWorld(): String {\n    return \"Hello, World!\"\n}"
                        },
                        "reasoning": "Creating a simple Hello World function with both main function and reusable function",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                }
            }
            userMessage.contains("user interaction test") -> {
                // Simulate a scenario where AI needs user input
                """
                {
                    "action": "WAIT_USER",
                    "reasoning": "I need clarification on where to create the file. Please specify the target directory or file path.",
                    "confidence": 0.8
                }
                """.trimIndent()
            }
            userMessage.contains("Calculator") -> {
                if (hasExecutionHistory) {
                    // Calculator has been created, complete the task
                    """
                    {
                        "action": "COMPLETE",
                        "reasoning": "Calculator class has been successfully created with all required arithmetic operations (add, subtract, multiply, divide)",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                } else {
                    // Create the Calculator class
                    """
                    {
                        "action": "EXECUTE_TOOL",
                        "toolName": "save-file",
                        "parameters": {
                            "path": "Calculator.kt",
                            "file_content": "class Calculator {\n    fun add(a: Double, b: Double): Double = a + b\n    \n    fun subtract(a: Double, b: Double): Double = a - b\n    \n    fun multiply(a: Double, b: Double): Double = a * b\n    \n    fun divide(a: Double, b: Double): Double {\n        require(b != 0.0) { \"Division by zero is not allowed\" }\n        return a / b\n    }\n}"
                        },
                        "reasoning": "Creating a Calculator class with the four basic arithmetic operations as requested",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                }
            }
            userMessage.contains("User") && userMessage.contains("class") -> {
                if (hasExecutionHistory) {
                    """
                    {
                        "action": "COMPLETE",
                        "reasoning": "User class has been successfully created with validation",
                        "confidence": 0.9
                    }
                    """.trimIndent()
                } else {
                    """
                    {
                        "action": "EXECUTE_TOOL",
                        "toolName": "save-file",
                        "parameters": {
                            "path": "User.kt",
                            "file_content": "data class User(\n    val id: Long,\n    val name: String,\n    val email: String\n) {\n    init {\n        require(name.isNotBlank()) { \"Name cannot be blank\" }\n        require(email.contains(\"@\")) { \"Invalid email format\" }\n    }\n}"
                        },
                        "reasoning": "Creating a User data class with validation as requested",
                        "confidence": 0.9
                    }
                    """.trimIndent()
                }
            }
            userMessage.contains("REST API") || userMessage.contains("API") -> {
                if (hasExecutionHistory) {
                    // Check how many files have been created based on execution history
                    val executionCount = userMessage.split("save-file: true").size - 1
                    when {
                        executionCount >= 3 -> {
                            """
                            {
                                "action": "COMPLETE",
                                "reasoning": "REST API has been successfully created with User model, UserService, and UserController",
                                "confidence": 0.9
                            }
                            """.trimIndent()
                        }
                        executionCount == 2 -> {
                            // Create UserController
                            """
                            {
                                "action": "EXECUTE_TOOL",
                                "toolName": "save-file",
                                "parameters": {
                                    "path": "UserController.kt",
                                    "file_content": "import org.springframework.web.bind.annotation.*\n\n@RestController\n@RequestMapping(\"/api/users\")\nclass UserController(private val userService: UserService) {\n    \n    @GetMapping\n    fun getAllUsers(): List<User> = userService.getAllUsers()\n    \n    @PostMapping\n    fun createUser(@RequestBody user: User): User = userService.createUser(user)\n    \n    @GetMapping(\"/{id}\")\n    fun getUserById(@PathVariable id: Long): User? = userService.getUserById(id)\n    \n    @PutMapping(\"/{id}\")\n    fun updateUser(@PathVariable id: Long, @RequestBody user: User): User = \n        userService.updateUser(id, user)\n    \n    @DeleteMapping(\"/{id}\")\n    fun deleteUser(@PathVariable id: Long) = userService.deleteUser(id)\n}"
                                },
                                "reasoning": "Creating UserController with REST endpoints to complete the API",
                                "confidence": 0.85
                            }
                            """.trimIndent()
                        }
                        executionCount == 1 -> {
                            // Create UserService
                            """
                            {
                                "action": "EXECUTE_TOOL",
                                "toolName": "save-file",
                                "parameters": {
                                    "path": "UserService.kt",
                                    "file_content": "import org.springframework.stereotype.Service\n\n@Service\nclass UserService {\n    \n    fun getAllUsers(): List<User> {\n        // TODO: Implement database access\n        return emptyList()\n    }\n    \n    fun createUser(user: User): User {\n        // TODO: Implement user creation\n        return user\n    }\n    \n    fun getUserById(id: Long): User? {\n        // TODO: Implement user retrieval\n        return null\n    }\n    \n    fun updateUser(id: Long, user: User): User {\n        // TODO: Implement user update\n        return user\n    }\n    \n    fun deleteUser(id: Long) {\n        // TODO: Implement user deletion\n    }\n}"
                                },
                                "reasoning": "Creating UserService for business logic as the second step",
                                "confidence": 0.85
                            }
                            """.trimIndent()
                        }
                        else -> {
                            // Create User model
                            """
                            {
                                "action": "EXECUTE_TOOL",
                                "toolName": "save-file",
                                "parameters": {
                                    "path": "User.kt",
                                    "file_content": "import javax.persistence.*\n\n@Entity\n@Table(name = \"users\")\ndata class User(\n    @Id\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\n    val id: Long = 0,\n    \n    @Column(nullable = false)\n    val name: String,\n    \n    @Column(nullable = false, unique = true)\n    val email: String\n)"
                                },
                                "reasoning": "Creating User entity as the first step for REST API development",
                                "confidence": 0.85
                            }
                            """.trimIndent()
                        }
                    }
                } else {
                    // Create User model as first step
                    """
                    {
                        "action": "EXECUTE_TOOL",
                        "toolName": "save-file",
                        "parameters": {
                            "path": "User.kt",
                            "file_content": "import javax.persistence.*\n\n@Entity\n@Table(name = \"users\")\ndata class User(\n    @Id\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\n    val id: Long = 0,\n    \n    @Column(nullable = false)\n    val name: String,\n    \n    @Column(nullable = false, unique = true)\n    val email: String\n)"
                        },
                        "reasoning": "Creating User entity as the first step for REST API development",
                        "confidence": 0.85
                    }
                    """.trimIndent()
                }
            }
            else -> {
                """
                {
                    "action": "COMPLETE",
                    "reasoning": "The requirement appears to be completed based on the current context",
                    "confidence": 0.8
                }
                """.trimIndent()
            }
        }
    }

    private fun generateAnalysisResponse(userMessage: String): String {
        return when {
            userMessage.contains("Calculator") -> {
                """
                {
                    "intent": "Create a Calculator class with basic arithmetic operations",
                    "complexity": "LOW",
                    "category": "CODE_GENERATION",
                    "prerequisites": [],
                    "estimatedSteps": 1,
                    "reasoning": "Simple class creation with basic methods, no external dependencies required"
                }
                """.trimIndent()
            }
            userMessage.contains("REST API") || userMessage.contains("API") -> {
                """
                {
                    "intent": "Create a complete REST API for user management with CRUD operations",
                    "complexity": "MEDIUM",
                    "category": "CODE_GENERATION",
                    "prerequisites": ["Spring Boot framework", "JPA dependencies"],
                    "estimatedSteps": 3,
                    "reasoning": "Requires creating model, service, and controller layers with proper annotations"
                }
                """.trimIndent()
            }
            else -> {
                """
                {
                    "intent": "Generic implementation task",
                    "complexity": "LOW",
                    "category": "OTHER",
                    "prerequisites": [],
                    "estimatedSteps": 1,
                    "reasoning": "Basic implementation task"
                }
                """.trimIndent()
            }
        }
    }

    private fun generateCompletionEvaluationResponse(userMessage: String): String {
        return """
        {
            "completed": true,
            "completionPercentage": 100,
            "missingItems": [],
            "summary": "The requirement has been successfully completed",
            "nextSteps": []
        }
        """.trimIndent()
    }

    /**
     * Handle conversation-based action decision requests
     */
    private fun handleConversationActionDecision(systemMessage: String, userMessage: String): String {
        // Extract requirement from system message
        val requirement = systemMessage.substringAfter("**Original Requirement**: ").substringBefore("\n")

        // Check if there are tool results in the conversation history
        val hasToolResults = systemMessage.contains("Tool execution completed successfully") ||
                           systemMessage.contains("Tool execution failed")

        return when {
            requirement.contains("Hello World") || requirement.contains("hello world") -> {
                if (hasToolResults) {
                    // Hello World function has been created, complete the task
                    """
                    {
                        "action": "COMPLETE",
                        "reasoning": "Hello World function has been successfully created",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                } else {
                    // Create the Hello World function
                    """
                    {
                        "action": "EXECUTE_TOOL",
                        "toolName": "save-file",
                        "parameters": {
                            "path": "HelloWorld.kt",
                            "file_content": "fun main() {\n    println(\"Hello, World!\")\n}\n\nfun helloWorld(): String {\n    return \"Hello, World!\"\n}"
                        },
                        "reasoning": "Creating a simple Hello World function with both main function and reusable function",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                }
            }
            requirement.contains("Calculator") -> {
                if (hasToolResults) {
                    """
                    {
                        "action": "COMPLETE",
                        "reasoning": "Calculator class has been successfully created with all required arithmetic operations",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                } else {
                    """
                    {
                        "action": "EXECUTE_TOOL",
                        "toolName": "save-file",
                        "parameters": {
                            "path": "Calculator.kt",
                            "file_content": "class Calculator {\n    fun add(a: Double, b: Double): Double = a + b\n    \n    fun subtract(a: Double, b: Double): Double = a - b\n    \n    fun multiply(a: Double, b: Double): Double = a * b\n    \n    fun divide(a: Double, b: Double): Double {\n        require(b != 0.0) { \"Division by zero is not allowed\" }\n        return a / b\n    }\n}"
                        },
                        "reasoning": "Creating a Calculator class with the four basic arithmetic operations as requested",
                        "confidence": 0.95
                    }
                    """.trimIndent()
                }
            }
            else -> {
                """
                {
                    "action": "COMPLETE",
                    "reasoning": "The requirement appears to be completed based on the current context",
                    "confidence": 0.8
                }
                """.trimIndent()
            }
        }
    }
}
