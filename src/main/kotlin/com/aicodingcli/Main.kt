package com.aicodingcli

import com.aicodingcli.ai.*
import com.aicodingcli.config.ConfigManager
import com.aicodingcli.conversation.*
import com.aicodingcli.debug.DebugManager
import com.aicodingcli.history.HistoryManager
import com.aicodingcli.history.HistorySearchCriteria
import com.aicodingcli.history.MessageTokenUsage
import com.aicodingcli.code.analysis.DefaultCodeAnalyzer
import com.aicodingcli.code.common.ProgrammingLanguage
import com.aicodingcli.interaction.UserInteractionManager
import com.aicodingcli.plugins.*
import kotlinx.coroutines.runBlocking
import java.io.File
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

fun main(args: Array<String>) {
    val cli = AiCodingCli()
    cli.run(args)
}

// Extension function for string repetition
private operator fun String.times(n: Int): String = this.repeat(n)

class AiCodingCli {
    companion object {
        const val VERSION = "0.1.0"
        const val HELP_TEXT = """AI Coding CLI v$VERSION - A powerful AI-assisted coding command line tool


🚀 USAGE:
  ai-coding-cli [COMMAND] [OPTIONS]

🤖 AI COMMANDS:
  test-connection              Test connection to AI service
  ask <message>                Ask AI a question or request assistance
  continuous <requirement>     Auto-execute complex tasks with AI guidance

📊 CODE ANALYSIS:
  analyze file <path>          Analyze a single code file
  analyze project <path>       Analyze an entire project
  analyze metrics <path>       Show detailed code metrics
  analyze issues <path>        Detect code quality issues

📝 CONVERSATION HISTORY:
  history list [--limit N]     List recent conversations
  history show <id>            Show conversation details
  history search <query>       Search conversations by content
  history delete <id>          Delete a specific conversation
  history clear                Clear all conversation history
  history stats                Show usage statistics

🔧 PLUGIN SYSTEM:
  plugin list                  List all installed plugins
  plugin install <path>        Install plugin from file or URL
  plugin uninstall <id>        Uninstall a plugin
  plugin enable <id>           Enable a plugin
  plugin disable <id>          Disable a plugin
  plugin info <id>             Show plugin information
  plugin validate <path>       Validate a plugin file

⚙️ CONFIGURATION:
  config set <key> <value>     Set configuration value
  config get <key>             Get configuration value
  config list                  List all configuration
  config provider <name>       Set default AI provider

🎛️ GLOBAL OPTIONS:
  --version                    Show version information
  --help                       Show this help message
  --debug                      Enable debug mode (show AI conversations)

🤖 AI OPTIONS:
  --provider <name>            AI provider (openai, claude, ollama, mock)
  --model <name>               Specific model to use
  --stream                     Enable real-time streaming response
  --continue <id>              Continue existing conversation
  --new                        Force start new conversation

📋 ANALYSIS OPTIONS:
  --format <type>              Output format (text, json)
  --language <lang>            Force language detection

💡 EXAMPLES:
  # Quick AI question
  ai-coding-cli ask "How to implement singleton in Kotlin?"

  # Stream response from Claude
  ai-coding-cli ask "Explain coroutines" --provider claude --stream

  # Continue previous conversation
  ai-coding-cli ask "Can you elaborate?" --continue abc123

  # Auto-execute complex tasks
  ai-coding-cli continuous "Create a User data class with validation"

  # Continue auto-execution session
  ai-coding-cli continuous --continue session-123

  # Analyze code file
  ai-coding-cli analyze file src/Main.kt --format json

  # Search conversation history
  ai-coding-cli history search "kotlin coroutines"

  # Configure OpenAI
  ai-coding-cli config set openai.api_key sk-your-key

  # Test connection
  ai-coding-cli test-connection --provider openai

🔗 For more information: https://github.com/iptton-ai/kbuildercli"""
    }

    private val configManager = ConfigManager()
    private val historyManager = HistoryManager()
    private val codeAnalyzer = DefaultCodeAnalyzer()
    private val userInteractionManager = UserInteractionManager()
    private val pluginManager = PluginManager(
        configManager = configManager,
        historyManager = historyManager,
        aiServiceFactory = AiServiceFactory
    )

    // Continuous conversation components
    private val conversationStateManager = ConversationStateManager()
    private val requirementParser = DefaultRequirementParser()
    private val toolExecutor = DefaultToolExecutor(
        workingDirectory = System.getProperty("user.dir")
    )

    // Create AI-driven task decomposer with AI service
    private fun createAiTaskDecomposer(): TaskDecomposer {
        return try {
            val config = runBlocking { configManager.getCurrentProviderConfig() }
            val aiService = AiServiceFactory.createService(config)
            val availableTools = toolExecutor.getSupportedTools()
            AiDrivenTaskDecomposer(aiService, availableTools)
        } catch (e: Exception) {
            // Fallback to default if AI service creation fails
            println("⚠️  AI service not configured, using basic task decomposer")
            DefaultTaskDecomposer()
        }
    }

    // Create AI-driven auto execution engine
    private fun createAiAutoExecutionEngine(): AutoExecutionEngine {
        return try {
            val config = runBlocking { configManager.getCurrentProviderConfig() }
            val aiService = AiServiceFactory.createService(config)
            val aiPromptEngine = DefaultAiPromptEngine(aiService, userInteractionManager)
            AiDrivenAutoExecutionEngine(
                conversationStateManager = conversationStateManager,
                aiPromptEngine = aiPromptEngine,
                toolExecutor = toolExecutor
            )
        } catch (e: Exception) {
            // Fallback to default if AI service creation fails
            println("⚠️  AI service not configured, using basic auto execution engine")
            DefaultAutoExecutionEngine(
                conversationStateManager = conversationStateManager,
                taskDecomposer = createAiTaskDecomposer(),
                requirementParser = requirementParser,
                toolExecutor = toolExecutor,
                maxExecutionRounds = 25
            )
        }
    }

    private val autoExecutionEngine = createAiAutoExecutionEngine()

    fun run(args: Array<String>) {
        val (command, options) = parseArgs(args)

        when {
            args.isEmpty() -> printHelp()
            command == "--version" -> printVersion()
            command == "--help" -> printHelp()
            command == "test-connection" -> testConnection(options.provider, options.model)
            command == "ask" && options.message.isNotEmpty() -> askQuestion(options.message, options.provider, options.model, options.stream, options.continueConversationId, options.forceNew)
            command == "continuous" -> handleContinuousCommand(args.drop(1).toTypedArray())
            command == "config" -> handleConfigCommand(args.drop(1).toTypedArray())
            command == "history" -> handleHistoryCommand(args.drop(1).toTypedArray())
            command == "analyze" -> handleAnalyzeCommand(args.drop(1).toTypedArray())
            command == "plugin" -> handlePluginCommand(args.drop(1).toTypedArray())
            else -> {
                println("Unknown command: $command")
                printHelp()
            }
        }
    }

    private data class CommandOptions(
        val provider: AiProvider? = null,
        val model: String? = null,
        val stream: Boolean = false,
        val continueConversationId: String? = null,
        val forceNew: Boolean = false,
        val debug: Boolean = false,
        val message: String = ""
    )

    private fun parseArgs(args: Array<String>): Pair<String, CommandOptions> {
        if (args.isEmpty()) return "" to CommandOptions()

        var command = args[0]
        var provider: AiProvider? = null
        var model: String? = null
        var stream = false
        var continueConversationId: String? = null
        var forceNew = false
        var debug = false
        var message = ""

        var i = 1
        while (i < args.size) {
            when (args[i]) {
                "--provider" -> {
                    if (i + 1 < args.size) {
                        provider = when (args[i + 1].lowercase()) {
                            "openai" -> AiProvider.OPENAI
                            "claude" -> AiProvider.CLAUDE
                            "ollama" -> AiProvider.OLLAMA
                            "mock" -> AiProvider.MOCK
                            else -> {
                                println("Unknown provider: ${args[i + 1]}")
                                return command to CommandOptions()
                            }
                        }
                        i += 2
                    } else {
                        println("--provider requires a value")
                        return command to CommandOptions()
                    }
                }
                "--model" -> {
                    if (i + 1 < args.size) {
                        model = args[i + 1]
                        i += 2
                    } else {
                        println("--model requires a value")
                        return command to CommandOptions()
                    }
                }
                "--stream" -> {
                    stream = true
                    i++
                }
                "--continue" -> {
                    if (i + 1 < args.size) {
                        continueConversationId = args[i + 1]
                        i += 2
                    } else {
                        println("--continue requires a conversation ID")
                        return command to CommandOptions()
                    }
                }
                "--new" -> {
                    forceNew = true
                    i++
                }
                "--debug" -> {
                    debug = true
                    DebugManager.setDebugMode(true)
                    i++
                }
                else -> {
                    if (command == "ask") {
                        message = args.drop(i).joinToString(" ")
                        break
                    }
                    i++
                }
            }
        }

        return command to CommandOptions(provider, model, stream, continueConversationId, forceNew, debug, message)
    }

    private fun printVersion() {
        println(VERSION)
    }

    private fun printHelp() {
        println(HELP_TEXT)
    }

    private fun testConnection(provider: AiProvider? = null, model: String? = null) {
        runBlocking {
            try {
                val config = if (provider != null) {
                    var providerConfig = getProviderConfig(provider)
                    if (model != null) {
                        providerConfig = providerConfig.copy(model = model)
                    }
                    providerConfig
                } else {
                    configManager.getCurrentProviderConfig()
                }
                val service = AiServiceFactory.createService(config)
                val result = service.testConnection()

                if (result) {
                    println("✅ Connection to ${config.provider} successful!")
                    if (model != null) {
                        println("   Using model: $model")
                    }
                } else {
                    println("❌ Connection to ${config.provider} failed!")
                }
            } catch (e: Exception) {
                println("❌ Error testing connection: ${e.message}")
            }
        }
    }

    private fun askQuestion(
        question: String,
        provider: AiProvider? = null,
        model: String? = null,
        stream: Boolean = false,
        continueConversationId: String? = null,
        forceNew: Boolean = false
    ) {
        runBlocking {
            try {
                val config = if (provider != null) {
                    var providerConfig = getProviderConfig(provider)
                    if (model != null) {
                        providerConfig = providerConfig.copy(model = model)
                    }
                    providerConfig
                } else {
                    configManager.getCurrentProviderConfig()
                }
                val service = AiServiceFactory.createService(config)

                // Determine conversation to use
                val conversation = when {
                    continueConversationId != null -> {
                        // User explicitly wants to continue a specific conversation
                        val existing = historyManager.getConversation(continueConversationId)
                        if (existing == null) {
                            println("❌ Conversation not found: $continueConversationId")
                            return@runBlocking
                        }

                        // Validate provider/model compatibility
                        if (provider != null && existing.provider != config.provider) {
                            println("⚠️  Warning: Switching provider from ${existing.provider} to ${config.provider}")
                        }
                        if (model != null && existing.model != config.model) {
                            println("⚠️  Warning: Switching model from ${existing.model} to ${config.model}")
                        }

                        existing
                    }
                    forceNew -> {
                        // User explicitly wants a new conversation
                        createNewConversation(question, config)
                    }
                    else -> {
                        // Smart conversation management: continue recent conversation if compatible
                        val recentConversations = historyManager.getAllConversations().take(5)
                        val compatibleConversation = recentConversations.find { conv ->
                            conv.provider == config.provider &&
                            conv.model == config.model &&
                            conv.messages.isNotEmpty()
                        }

                        if (compatibleConversation != null) {
                            println("🔄 Continuing conversation: ${compatibleConversation.title} (${compatibleConversation.id.take(8)})")
                            compatibleConversation
                        } else {
                            createNewConversation(question, config)
                        }
                    }
                }

                // Add user message to history
                historyManager.addMessage(
                    conversationId = conversation.id,
                    role = MessageRole.USER,
                    content = question
                )

                // Build message history for context
                val messages = buildMessageHistory(conversation, question)

                val request = AiRequest(
                    messages = messages,
                    model = config.model,
                    temperature = config.temperature,
                    maxTokens = config.maxTokens,
                    stream = stream
                )

                println("🤖 Asking ${config.provider}...")
                if (model != null) {
                    println("   Using model: $model")
                }
                if (messages.size > 1) {
                    println("   Context: ${messages.size - 1} previous messages")
                }
                if (stream) {
                    println("   Streaming mode enabled")
                    println()

                    // Handle streaming response
                    val responseBuilder = StringBuilder()
                    service.streamChat(request).collect { chunk ->
                        print(chunk.content)
                        System.out.flush()
                        responseBuilder.append(chunk.content)

                        if (chunk.finishReason != null) {
                            println("\n")
                            println("📊 Streaming response completed")

                            // Add assistant response to history
                            historyManager.addMessage(
                                conversationId = conversation.id,
                                role = MessageRole.ASSISTANT,
                                content = responseBuilder.toString()
                            )
                        }
                    }
                } else {
                    // Handle regular response
                    val response = service.chat(request)
                    println("\n${response.content}")
                    println("\n📊 Usage: ${response.usage.totalTokens} tokens")

                    // Add assistant response to history
                    historyManager.addMessage(
                        conversationId = conversation.id,
                        role = MessageRole.ASSISTANT,
                        content = response.content,
                        tokenUsage = MessageTokenUsage(
                            promptTokens = response.usage.promptTokens,
                            completionTokens = response.usage.completionTokens,
                            totalTokens = response.usage.totalTokens
                        )
                    )
                }

                println("\n💾 Conversation ID: ${conversation.id.take(8)} (${conversation.messages.size} messages)")

            } catch (e: Exception) {
                println("❌ Error asking question: ${e.message}")
            }
        }
    }

    private fun createNewConversation(question: String, config: AiServiceConfig) =
        historyManager.createConversation(
            title = generateConversationTitle(question),
            provider = config.provider,
            model = config.model
        )

    private fun buildMessageHistory(conversation: com.aicodingcli.history.ConversationSession, currentQuestion: String): List<AiMessage> {
        val messages = mutableListOf<AiMessage>()

        // Add previous messages (limit to last 10 to avoid token limits)
        val recentMessages = conversation.messages.takeLast(10)
        recentMessages.forEach { historyMessage ->
            messages.add(AiMessage(
                role = historyMessage.role,
                content = historyMessage.content
            ))
        }

        // Add current question
        messages.add(AiMessage(
            role = MessageRole.USER,
            content = currentQuestion
        ))

        return messages
    }

    private fun generateConversationTitle(question: String): String {
        // Generate a meaningful title from the question
        val words = question.split(" ").take(5)
        return if (words.size <= 5) {
            question
        } else {
            "${words.joinToString(" ")}..."
        }
    }

    private suspend fun getProviderConfig(provider: AiProvider): AiServiceConfig {
        val config = configManager.loadConfig()
        return config.providers[provider]
            ?: createDefaultProviderConfig(provider)
    }

    private fun createDefaultProviderConfig(provider: AiProvider): AiServiceConfig {
        return when (provider) {
            AiProvider.OPENAI -> AiServiceConfig(
                provider = AiProvider.OPENAI,
                apiKey = "your-openai-api-key",
                model = "gpt-3.5-turbo",
                baseUrl = "https://api.openai.com/v1",
                temperature = 0.7f,
                maxTokens = 1000
            )
            AiProvider.CLAUDE -> AiServiceConfig(
                provider = AiProvider.CLAUDE,
                apiKey = "your-claude-api-key",
                model = "claude-3-sonnet-20240229",
                baseUrl = "https://api.anthropic.com/v1",
                temperature = 0.7f,
                maxTokens = 1000
            )

            AiProvider.OLLAMA -> AiServiceConfig(
                provider = AiProvider.OLLAMA,
                apiKey = "not-required", // Ollama doesn't require API key but config validation needs non-empty string
                model = "llama2",
                baseUrl = "http://localhost:11434",
                temperature = 0.7f,
                maxTokens = 1000
            )
            AiProvider.MOCK -> AiServiceConfig(
                provider = AiProvider.MOCK,
                apiKey = "mock-key", // Mock doesn't require real API key
                model = "mock-model",
                baseUrl = "http://localhost:8080",
                temperature = 0.3f,
                maxTokens = 2000
            )
        }
    }

    private fun handleConfigCommand(args: Array<String>) {
        if (args.isEmpty()) {
            printConfigHelp()
            return
        }

        when (args[0]) {
            "set" -> handleConfigSet(args.drop(1).toTypedArray())
            "get" -> handleConfigGet(args.drop(1).toTypedArray())
            "list" -> handleConfigList()
            "provider" -> handleConfigProvider(args.drop(1).toTypedArray())
            else -> {
                println("Unknown config subcommand: ${args[0]}")
                printConfigHelp()
            }
        }
    }

    private fun printConfigHelp() {
        println("""
            Configuration Management Commands:

            config set <key> <value>    Set a configuration value
            config get <key>            Get a configuration value
            config list                 List all configuration
            config provider <name>      Set default AI provider

            Examples:
            config set openai.api_key sk-...
            config set claude.api_key sk-ant-...
            config get openai.api_key
            config provider ollama
            config list
        """.trimIndent())
    }

    private fun handleConfigSet(args: Array<String>) {
        if (args.size < 2) {
            println("Usage: config set <key> <value>")
            return
        }

        val key = args[0]
        val value = args.drop(1).joinToString(" ")

        runBlocking {
            try {
                setConfigValue(key, value)
                println("✅ Configuration updated: $key")
            } catch (e: Exception) {
                println("❌ Error setting configuration: ${e.message}")
            }
        }
    }

    private fun handleConfigGet(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: config get <key>")
            return
        }

        val key = args[0]

        runBlocking {
            try {
                val value = getConfigValue(key)
                if (value != null) {
                    // Mask sensitive values
                    val displayValue = if (key.contains("api_key") || key.contains("apikey")) {
                        maskApiKey(value)
                    } else {
                        value
                    }
                    println("$key = $displayValue")
                } else {
                    println("Configuration key '$key' not found")
                }
            } catch (e: Exception) {
                println("❌ Error getting configuration: ${e.message}")
            }
        }
    }

    private fun handleConfigList() {
        runBlocking {
            try {
                val config = configManager.loadConfig()
                println("Current Configuration:")
                println("Default Provider: ${config.defaultProvider}")
                println()

                config.providers.forEach { (provider, providerConfig) ->
                    println("[$provider]")
                    println("  api_key = ${maskApiKey(providerConfig.apiKey)}")
                    println("  model = ${providerConfig.model}")
                    println("  base_url = ${providerConfig.baseUrl ?: "default"}")
                    println("  temperature = ${providerConfig.temperature}")
                    println("  max_tokens = ${providerConfig.maxTokens}")
                    println()
                }
            } catch (e: Exception) {
                println("❌ Error listing configuration: ${e.message}")
            }
        }
    }

    private fun handleConfigProvider(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: config provider <name>")
            println("Available providers: openai, claude, ollama, mock")
            return
        }

        val providerName = args[0].lowercase()
        val provider = when (providerName) {
            "openai" -> AiProvider.OPENAI
            "claude" -> AiProvider.CLAUDE
            "ollama" -> AiProvider.OLLAMA
            "mock" -> AiProvider.MOCK
            else -> {
                println("Unknown provider: $providerName")
                println("Available providers: openai, claude, ollama, mock")
                return
            }
        }

        runBlocking {
            try {
                configManager.setDefaultProvider(provider)
                println("✅ Default provider set to: $provider")
            } catch (e: Exception) {
                println("❌ Error setting default provider: ${e.message}")
            }
        }
    }

    private suspend fun setConfigValue(key: String, value: String) {
        val parts = key.split(".")
        if (parts.size != 2) {
            throw IllegalArgumentException("Key must be in format: provider.property (e.g., openai.api_key)")
        }

        val providerName = parts[0].lowercase()
        val property = parts[1].lowercase()

        val provider = when (providerName) {
            "openai" -> AiProvider.OPENAI
            "claude" -> AiProvider.CLAUDE
            "ollama" -> AiProvider.OLLAMA
            "mock" -> AiProvider.MOCK
            else -> throw IllegalArgumentException("Unknown provider: $providerName")
        }

        val config = configManager.loadConfig()
        val currentProviderConfig = config.providers[provider] ?: createDefaultProviderConfig(provider)

        val updatedConfig = when (property) {
            "api_key", "apikey" -> currentProviderConfig.copy(apiKey = value)
            "model" -> currentProviderConfig.copy(model = value)
            "base_url", "baseurl" -> currentProviderConfig.copy(baseUrl = value)
            "temperature" -> currentProviderConfig.copy(temperature = value.toFloatOrNull()
                ?: throw IllegalArgumentException("Temperature must be a number"))
            "max_tokens", "maxtokens" -> currentProviderConfig.copy(maxTokens = value.toIntOrNull()
                ?: throw IllegalArgumentException("Max tokens must be a number"))
            else -> throw IllegalArgumentException("Unknown property: $property")
        }

        configManager.updateProviderConfig(provider, updatedConfig)
    }

    private suspend fun getConfigValue(key: String): String? {
        val parts = key.split(".")
        if (parts.size != 2) {
            return null
        }

        val providerName = parts[0].lowercase()
        val property = parts[1].lowercase()

        val provider = when (providerName) {
            "openai" -> AiProvider.OPENAI
            "claude" -> AiProvider.CLAUDE
            "ollama" -> AiProvider.OLLAMA
            "mock" -> AiProvider.MOCK
            else -> return null
        }

        val config = configManager.loadConfig()
        val providerConfig = config.providers[provider] ?: return null

        return when (property) {
            "api_key", "apikey" -> providerConfig.apiKey
            "model" -> providerConfig.model
            "base_url", "baseurl" -> providerConfig.baseUrl
            "temperature" -> providerConfig.temperature.toString()
            "max_tokens", "maxtokens" -> providerConfig.maxTokens.toString()
            else -> null
        }
    }

    private fun maskApiKey(apiKey: String): String {
        return if (apiKey.length > 8) {
            "${apiKey.take(4)}${"*".repeat(apiKey.length - 8)}${apiKey.takeLast(4)}"
        } else {
            "*".repeat(apiKey.length)
        }
    }

    private fun handleHistoryCommand(args: Array<String>) {
        if (args.isEmpty()) {
            printHistoryHelp()
            return
        }

        when (args[0]) {
            "list" -> handleHistoryList(args.drop(1).toTypedArray())
            "show" -> handleHistoryShow(args.drop(1).toTypedArray())
            "search" -> handleHistorySearch(args.drop(1).toTypedArray())
            "delete" -> handleHistoryDelete(args.drop(1).toTypedArray())
            "clear" -> handleHistoryClear()
            "stats" -> handleHistoryStats()
            else -> {
                println("Unknown history subcommand: ${args[0]}")
                printHistoryHelp()
            }
        }
    }

    private fun printHistoryHelp() {
        println("""
            Conversation History Management Commands:

            history list [--limit N]        List recent conversations
            history show <id>               Show conversation details
            history search <query>          Search conversations
            history delete <id>             Delete a conversation
            history clear                   Clear all conversations
            history stats                   Show history statistics

            Examples:
            history list --limit 10
            history show abc123
            history search "kotlin"
            history delete abc123
        """.trimIndent())
    }

    private fun handleHistoryList(args: Array<String>) {
        var limit = 20

        // Parse limit argument
        var i = 0
        while (i < args.size) {
            when (args[i]) {
                "--limit" -> {
                    if (i + 1 < args.size) {
                        limit = args[i + 1].toIntOrNull() ?: 20
                        i += 2
                    } else {
                        println("--limit requires a value")
                        return
                    }
                }
                else -> i++
            }
        }

        val conversations = historyManager.getAllConversations().take(limit)

        if (conversations.isEmpty()) {
            println("No conversation history found.")
            return
        }

        println("Recent Conversations:")
        println("=" * 60)

        conversations.forEach { conversation ->
            val date = formatTimestamp(conversation.updatedAt)
            println("ID: ${conversation.id.take(8)}")
            println("Title: ${conversation.title}")
            println("Provider: ${conversation.provider} (${conversation.model})")
            println("Updated: $date")
            println("Messages: ${conversation.messages.size}")
            println("Summary: ${conversation.getSummary()}")
            println("-" * 40)
        }
    }

    private fun handleHistoryShow(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: history show <conversation-id>")
            return
        }

        val conversationId = args[0]
        val conversation = historyManager.getConversation(conversationId)

        if (conversation == null) {
            println("Conversation not found: $conversationId")
            return
        }

        println("Conversation: ${conversation.title}")
        println("ID: ${conversation.id}")
        println("Provider: ${conversation.provider} (${conversation.model})")
        println("Created: ${formatTimestamp(conversation.createdAt)}")
        println("Updated: ${formatTimestamp(conversation.updatedAt)}")
        println("Messages: ${conversation.messages.size}")
        println("=" * 60)

        conversation.messages.forEach { message ->
            val timestamp = formatTimestamp(message.timestamp)
            val role = when (message.role) {
                MessageRole.USER -> "👤 User"
                MessageRole.ASSISTANT -> "🤖 Assistant"
                MessageRole.SYSTEM -> "⚙️ System"
            }

            println("[$timestamp] $role:")
            println(message.content)

            message.tokenUsage?.let { usage ->
                println("📊 Tokens: ${usage.totalTokens} (prompt: ${usage.promptTokens}, completion: ${usage.completionTokens})")
            }

            println("-" * 40)
        }
    }

    private fun handleHistorySearch(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: history search <query>")
            return
        }

        val query = args.joinToString(" ")
        val criteria = HistorySearchCriteria(query = query, limit = 20)
        val conversations = historyManager.searchConversations(criteria)

        if (conversations.isEmpty()) {
            println("No conversations found matching: $query")
            return
        }

        println("Search Results for: $query")
        println("=" * 60)

        conversations.forEach { conversation ->
            val date = formatTimestamp(conversation.updatedAt)
            println("ID: ${conversation.id.take(8)}")
            println("Title: ${conversation.title}")
            println("Provider: ${conversation.provider} (${conversation.model})")
            println("Updated: $date")
            println("Summary: ${conversation.getSummary()}")
            println("-" * 40)
        }
    }

    private fun handleHistoryDelete(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: history delete <conversation-id>")
            return
        }

        val conversationId = args[0]
        val deleted = historyManager.deleteConversation(conversationId)

        if (deleted) {
            println("✅ Conversation deleted: $conversationId")
        } else {
            println("❌ Conversation not found: $conversationId")
        }
    }

    private fun handleHistoryClear() {
        print("Are you sure you want to clear all conversation history? (y/N): ")
        val confirmation = readlnOrNull()?.lowercase()

        if (confirmation == "y" || confirmation == "yes") {
            historyManager.clearAllConversations()
            println("✅ All conversation history cleared.")
        } else {
            println("Operation cancelled.")
        }
    }

    private fun handleHistoryStats() {
        val stats = historyManager.getStatistics()

        println("Conversation History Statistics:")
        println("=" * 40)
        println("Total Conversations: ${stats.totalConversations}")
        println("Total Messages: ${stats.totalMessages}")
        println("Total Tokens Used: ${stats.totalTokensUsed}")
        println()

        if (stats.providerBreakdown.isNotEmpty()) {
            println("Provider Breakdown:")
            stats.providerBreakdown.forEach { (provider, count) ->
                println("  $provider: $count conversations")
            }
            println()
        }

        stats.oldestConversation?.let { oldest ->
            println("Oldest Conversation: ${formatTimestamp(oldest)}")
        }

        stats.newestConversation?.let { newest ->
            println("Newest Conversation: ${formatTimestamp(newest)}")
        }
    }

    private fun formatTimestamp(epochSecond: Long): String {
        val dateTime = LocalDateTime.ofInstant(
            Instant.ofEpochSecond(epochSecond),
            ZoneId.systemDefault()
        )
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    }

    private operator fun String.times(n: Int): String = this.repeat(n)

    // Code Analysis Commands
    private fun handleAnalyzeCommand(args: Array<String>) {
        if (args.isEmpty()) {
            printAnalyzeHelp()
            return
        }

        when (args[0]) {
            "file" -> handleAnalyzeFile(args.drop(1).toTypedArray())
            "project" -> handleAnalyzeProject(args.drop(1).toTypedArray())
            "metrics" -> handleAnalyzeMetrics(args.drop(1).toTypedArray())
            "issues" -> handleAnalyzeIssues(args.drop(1).toTypedArray())
            else -> {
                println("Unknown analyze subcommand: ${args[0]}")
                printAnalyzeHelp()
            }
        }
    }

    private fun printAnalyzeHelp() {
        println("""
            Code Analysis Commands:

            analyze file <file-path>        Analyze a single file
            analyze project <project-path>  Analyze an entire project
            analyze metrics <file-path>     Show code metrics for a file
            analyze issues <file-path>      Show code issues for a file

            Options:
            --format <format>               Output format (text, json) [default: text]
            --language <lang>               Force language detection (kotlin, java, python)

            Examples:
            analyze file src/main/kotlin/Main.kt
            analyze project src/main/kotlin
            analyze metrics --format json src/main/kotlin/Main.kt
            analyze issues src/main/kotlin/Main.kt
        """.trimIndent())
    }

    private fun handleAnalyzeFile(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: analyze file <file-path> [--format <format>] [--language <lang>]")
            return
        }

        val filePath = args[0]
        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())

        runBlocking {
            try {
                val result = codeAnalyzer.analyzeFile(filePath)
                displayAnalysisResult(result, options.format)
            } catch (e: Exception) {
                println("❌ Error analyzing file: ${e.message}")
            }
        }
    }

    private fun handleAnalyzeProject(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: analyze project <project-path> [--format <format>]")
            return
        }

        val projectPath = args[0]
        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())

        runBlocking {
            try {
                val result = codeAnalyzer.analyzeProject(projectPath)
                displayProjectAnalysisResult(result, options.format)
            } catch (e: Exception) {
                println("❌ Error analyzing project: ${e.message}")
            }
        }
    }

    private fun handleAnalyzeMetrics(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: analyze metrics <file-path> [--format <format>]")
            return
        }

        val filePath = args[0]
        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())

        runBlocking {
            try {
                val result = codeAnalyzer.analyzeFile(filePath)
                displayMetrics(result.metrics, options.format)
            } catch (e: Exception) {
                println("❌ Error analyzing metrics: ${e.message}")
            }
        }
    }

    private fun handleAnalyzeIssues(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: analyze issues <file-path> [--format <format>]")
            return
        }

        val filePath = args[0]
        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())

        runBlocking {
            try {
                val result = codeAnalyzer.analyzeFile(filePath)
                displayIssues(result.issues, options.format)
            } catch (e: Exception) {
                println("❌ Error analyzing issues: ${e.message}")
            }
        }
    }

    private data class AnalyzeOptions(
        val format: String = "text",
        val language: ProgrammingLanguage? = null
    )

    private fun parseAnalyzeOptions(args: Array<String>): AnalyzeOptions {
        var format = "text"
        var language: ProgrammingLanguage? = null

        var i = 0
        while (i < args.size) {
            when (args[i]) {
                "--format" -> {
                    if (i + 1 < args.size) {
                        format = args[i + 1]
                        i += 2
                    } else {
                        println("--format requires a value")
                        i++
                    }
                }
                "--language" -> {
                    if (i + 1 < args.size) {
                        language = when (args[i + 1].lowercase()) {
                            "kotlin" -> ProgrammingLanguage.KOTLIN
                            "java" -> ProgrammingLanguage.JAVA
                            "python" -> ProgrammingLanguage.PYTHON
                            "javascript" -> ProgrammingLanguage.JAVASCRIPT
                            "typescript" -> ProgrammingLanguage.TYPESCRIPT
                            else -> {
                                println("Unknown language: ${args[i + 1]}")
                                null
                            }
                        }
                        i += 2
                    } else {
                        println("--language requires a value")
                        i++
                    }
                }
                else -> i++
            }
        }

        return AnalyzeOptions(format, language)
    }

    private fun displayAnalysisResult(result: com.aicodingcli.code.analysis.CodeAnalysisResult, format: String) {
        when (format.lowercase()) {
            "json" -> displayAnalysisResultJson(result)
            else -> displayAnalysisResultText(result)
        }
    }

    private fun displayAnalysisResultText(result: com.aicodingcli.code.analysis.CodeAnalysisResult) {
        println("📊 Code Analysis Results")
        println("=" * 50)
        println("File: ${result.filePath}")
        println("Language: ${result.language.displayName}")
        println()

        // Display metrics
        println("📈 Metrics:")
        println("  Lines of Code: ${result.metrics.linesOfCode}")
        println("  Cyclomatic Complexity: ${result.metrics.cyclomaticComplexity}")
        println("  Maintainability Index: ${"%.1f".format(result.metrics.maintainabilityIndex)}")
        result.metrics.testCoverage?.let { coverage ->
            println("  Test Coverage: ${"%.1f".format(coverage)}%")
        }
        println("  Duplicated Lines: ${result.metrics.duplicatedLines}")
        println()

        // Display issues
        if (result.issues.isNotEmpty()) {
            println("⚠️  Issues (${result.issues.size}):")
            result.issues.forEach { issue ->
                val severity = when (issue.severity) {
                    com.aicodingcli.code.analysis.IssueSeverity.CRITICAL -> "🔴 CRITICAL"
                    com.aicodingcli.code.analysis.IssueSeverity.HIGH -> "🟠 HIGH"
                    com.aicodingcli.code.analysis.IssueSeverity.MEDIUM -> "🟡 MEDIUM"
                    com.aicodingcli.code.analysis.IssueSeverity.LOW -> "🟢 LOW"
                }
                val location = if (issue.line != null) " (line ${issue.line})" else ""
                println("  $severity: ${issue.message}$location")
                issue.suggestion?.let { suggestion ->
                    println("    💡 Suggestion: $suggestion")
                }
            }
            println()
        }

        // Display suggestions
        if (result.suggestions.isNotEmpty()) {
            println("💡 Improvement Suggestions (${result.suggestions.size}):")
            result.suggestions.forEach { suggestion ->
                val priority = when (suggestion.priority) {
                    com.aicodingcli.code.analysis.ImprovementPriority.HIGH -> "🔴 HIGH"
                    com.aicodingcli.code.analysis.ImprovementPriority.MEDIUM -> "🟡 MEDIUM"
                    com.aicodingcli.code.analysis.ImprovementPriority.LOW -> "🟢 LOW"
                }
                val location = if (suggestion.line != null) " (line ${suggestion.line})" else ""
                println("  $priority: ${suggestion.description}$location")
            }
            println()
        }

        // Display dependencies
        if (result.dependencies.isNotEmpty()) {
            println("📦 Dependencies (${result.dependencies.size}):")
            result.dependencies.forEach { dependency ->
                println("  ${dependency.name} (${dependency.version ?: "unknown"}) - ${dependency.type}")
            }
        }
    }

    private fun displayAnalysisResultJson(result: com.aicodingcli.code.analysis.CodeAnalysisResult) {
        // Simple JSON output - in a real implementation, you'd use a JSON library
        println("""{
  "filePath": "${result.filePath}",
  "language": "${result.language.displayName}",
  "metrics": {
    "linesOfCode": ${result.metrics.linesOfCode},
    "cyclomaticComplexity": ${result.metrics.cyclomaticComplexity},
    "maintainabilityIndex": ${result.metrics.maintainabilityIndex},
    "testCoverage": ${result.metrics.testCoverage},
    "duplicatedLines": ${result.metrics.duplicatedLines}
  },
  "issuesCount": ${result.issues.size},
  "suggestionsCount": ${result.suggestions.size},
  "dependenciesCount": ${result.dependencies.size}
}""")
    }

    private fun displayProjectAnalysisResult(result: com.aicodingcli.code.analysis.ProjectAnalysisResult, format: String) {
        when (format.lowercase()) {
            "json" -> displayProjectAnalysisResultJson(result)
            else -> displayProjectAnalysisResultText(result)
        }
    }

    private fun displayProjectAnalysisResultText(result: com.aicodingcli.code.analysis.ProjectAnalysisResult) {
        println("📊 Project Analysis Results")
        println("=" * 50)
        println("Project: ${result.projectPath}")
        println("Files Analyzed: ${result.fileResults.size}")
        println()

        // Display overall metrics
        println("📈 Overall Metrics:")
        println("  Total Lines of Code: ${result.overallMetrics.linesOfCode}")
        println("  Average Complexity: ${result.overallMetrics.cyclomaticComplexity}")
        println("  Average Maintainability: ${"%.1f".format(result.overallMetrics.maintainabilityIndex)}")
        println()

        // Display summary
        println("📋 Summary:")
        println("  Total Files: ${result.summary.totalFiles}")
        println("  Total Issues: ${result.summary.totalIssues}")
        println("  Critical Issues: ${result.summary.criticalIssues}")
        println("  Average Complexity: ${"%.1f".format(result.summary.averageComplexity)}")
        println("  Overall Maintainability: ${"%.1f".format(result.summary.overallMaintainabilityIndex)}")
        println()

        // Display top issues by file
        val filesWithIssues = result.fileResults.filter { it.issues.isNotEmpty() }
        if (filesWithIssues.isNotEmpty()) {
            println("⚠️  Files with Issues:")
            filesWithIssues.sortedByDescending { it.issues.size }.take(10).forEach { fileResult ->
                val fileName = File(fileResult.filePath).name
                println("  $fileName: ${fileResult.issues.size} issues")
            }
        }
    }

    private fun displayProjectAnalysisResultJson(result: com.aicodingcli.code.analysis.ProjectAnalysisResult) {
        println("""{
  "projectPath": "${result.projectPath}",
  "filesAnalyzed": ${result.fileResults.size},
  "overallMetrics": {
    "linesOfCode": ${result.overallMetrics.linesOfCode},
    "averageComplexity": ${result.overallMetrics.cyclomaticComplexity},
    "averageMaintainability": ${result.overallMetrics.maintainabilityIndex}
  },
  "summary": {
    "totalFiles": ${result.summary.totalFiles},
    "totalIssues": ${result.summary.totalIssues},
    "criticalIssues": ${result.summary.criticalIssues},
    "averageComplexity": ${result.summary.averageComplexity},
    "overallMaintainability": ${result.summary.overallMaintainabilityIndex}
  }
}""")
    }

    private fun displayMetrics(metrics: com.aicodingcli.code.analysis.CodeMetrics, format: String) {
        when (format.lowercase()) {
            "json" -> {
                println("""{
  "linesOfCode": ${metrics.linesOfCode},
  "cyclomaticComplexity": ${metrics.cyclomaticComplexity},
  "maintainabilityIndex": ${metrics.maintainabilityIndex},
  "testCoverage": ${metrics.testCoverage},
  "duplicatedLines": ${metrics.duplicatedLines}
}""")
            }
            else -> {
                println("📈 Code Metrics:")
                println("  Lines of Code: ${metrics.linesOfCode}")
                println("  Cyclomatic Complexity: ${metrics.cyclomaticComplexity}")
                println("  Maintainability Index: ${"%.1f".format(metrics.maintainabilityIndex)}")
                metrics.testCoverage?.let { coverage ->
                    println("  Test Coverage: ${"%.1f".format(coverage)}%")
                }
                println("  Duplicated Lines: ${metrics.duplicatedLines}")
            }
        }
    }

    private fun displayIssues(issues: List<com.aicodingcli.code.analysis.CodeIssue>, format: String) {
        when (format.lowercase()) {
            "json" -> {
                println("[")
                issues.forEachIndexed { index, issue ->
                    println("""  {
    "type": "${issue.type}",
    "severity": "${issue.severity}",
    "message": "${issue.message}",
    "line": ${issue.line},
    "column": ${issue.column},
    "suggestion": "${issue.suggestion}"
  }${if (index < issues.size - 1) "," else ""}""")
                }
                println("]")
            }
            else -> {
                if (issues.isEmpty()) {
                    println("✅ No issues found!")
                } else {
                    println("⚠️  Code Issues (${issues.size}):")
                    issues.forEach { issue ->
                        val severity = when (issue.severity) {
                            com.aicodingcli.code.analysis.IssueSeverity.CRITICAL -> "🔴 CRITICAL"
                            com.aicodingcli.code.analysis.IssueSeverity.HIGH -> "🟠 HIGH"
                            com.aicodingcli.code.analysis.IssueSeverity.MEDIUM -> "🟡 MEDIUM"
                            com.aicodingcli.code.analysis.IssueSeverity.LOW -> "🟢 LOW"
                        }
                        val location = if (issue.line != null) " (line ${issue.line})" else ""
                        println("  $severity: ${issue.message}$location")
                        issue.suggestion?.let { suggestion ->
                            println("    💡 Suggestion: $suggestion")
                        }
                    }
                }
            }
        }
    }

    private fun handlePluginCommand(args: Array<String>) {
        if (args.isEmpty()) {
            printPluginHelp()
            return
        }

        when (args[0]) {
            "list" -> handlePluginList(args.drop(1).toTypedArray())
            "install" -> handlePluginInstall(args.drop(1).toTypedArray())
            "uninstall" -> handlePluginUninstall(args.drop(1).toTypedArray())
            "enable" -> handlePluginEnable(args.drop(1).toTypedArray())
            "disable" -> handlePluginDisable(args.drop(1).toTypedArray())
            "info" -> handlePluginInfo(args.drop(1).toTypedArray())
            "validate" -> handlePluginValidate(args.drop(1).toTypedArray())
            else -> {
                println("Unknown plugin subcommand: ${args[0]}")
                printPluginHelp()
            }
        }
    }

    private fun printPluginHelp() {
        println("""
            Plugin Management Commands:

            plugin list                     List all installed plugins
            plugin install <path-or-url>    Install a plugin from file or URL
            plugin uninstall <plugin-id>    Uninstall a plugin
            plugin enable <plugin-id>       Enable a plugin
            plugin disable <plugin-id>      Disable a plugin
            plugin info <plugin-id>         Show plugin information
            plugin validate <plugin-path>   Validate a plugin file

            Examples:
            plugin list
            plugin install ./my-plugin.jar
            plugin install https://example.com/plugin.jar
            plugin uninstall my-plugin-id
            plugin info my-plugin-id
            plugin validate ./my-plugin.jar
        """.trimIndent())
    }

    private fun handlePluginList(args: Array<String>) {
        try {
            val loadedPlugins = pluginManager.getLoadedPlugins()
            val discoveryService = PluginDiscoveryService(
                System.getProperty("user.home") + "/.aicodingcli/plugins"
            )
            val availablePlugins = discoveryService.discoverPlugins()

            if (loadedPlugins.isEmpty() && availablePlugins.isEmpty()) {
                println("No plugins found.")
                return
            }

            println("Plugin Status:")
            println("=" * 60)

            // Show loaded plugins
            if (loadedPlugins.isNotEmpty()) {
                println("\n✅ Loaded Plugins (${loadedPlugins.size}):")
                loadedPlugins.forEach { plugin ->
                    val state = pluginManager.getPluginState(plugin.metadata.id) ?: PluginState.UNLOADED
                    println("  📦 ${plugin.metadata.name} (${plugin.metadata.id})")
                    println("     Version: ${plugin.metadata.version}")
                    println("     Author: ${plugin.metadata.author}")
                    println("     State: $state")
                    println("     Description: ${plugin.metadata.description}")

                    when (plugin) {
                        is CommandPlugin -> {
                            println("     Type: Command Plugin")
                            println("     Commands: ${plugin.commands.map { it.name }.joinToString(", ")}")
                        }
                        is AiServicePlugin -> {
                            println("     Type: AI Service Plugin")
                            println("     Provider: ${plugin.supportedProvider}")
                        }
                        else -> {
                            println("     Type: Generic Plugin")
                        }
                    }
                    println()
                }
            }

            // Show available but not loaded plugins
            val loadedIds = loadedPlugins.map { it.metadata.id }.toSet()
            val unloadedPlugins = availablePlugins.filter { it.metadata.id !in loadedIds }

            if (unloadedPlugins.isNotEmpty()) {
                println("📋 Available Plugins (${unloadedPlugins.size}):")
                unloadedPlugins.forEach { pluginInfo ->
                    println("  📦 ${pluginInfo.metadata.name} (${pluginInfo.metadata.id})")
                    println("     Version: ${pluginInfo.metadata.version}")
                    println("     Author: ${pluginInfo.metadata.author}")
                    println("     File: ${pluginInfo.filePath}")
                    println("     Description: ${pluginInfo.metadata.description}")
                    println()
                }
            }

            // Show registry statistics
            val stats = pluginManager.getRegistry().getStatistics()
            println("📊 Plugin Statistics:")
            println("  Total Plugins: ${stats.totalPlugins}")
            println("  Command Plugins: ${stats.commandPlugins}")
            println("  AI Service Plugins: ${stats.aiServicePlugins}")
            println("  Total Commands: ${stats.totalCommands}")
            if (stats.supportedAiProviders.isNotEmpty()) {
                println("  Supported AI Providers: ${stats.supportedAiProviders.joinToString(", ")}")
            }

        } catch (e: Exception) {
            println("❌ Error listing plugins: ${e.message}")
        }
    }

    private fun handlePluginInstall(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: plugin install <path-or-url>")
            return
        }

        val pluginSource = args[0]

        runBlocking {
            try {
                println("📦 Installing plugin from: $pluginSource")
                val success = pluginManager.installPlugin(pluginSource)

                if (success) {
                    println("✅ Plugin installed successfully!")
                } else {
                    println("❌ Plugin installation failed!")
                }
            } catch (e: Exception) {
                println("❌ Error installing plugin: ${e.message}")
            }
        }
    }

    private fun handlePluginUninstall(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: plugin uninstall <plugin-id>")
            return
        }

        val pluginId = args[0]

        runBlocking {
            try {
                println("🗑️  Uninstalling plugin: $pluginId")
                val success = pluginManager.uninstallPlugin(pluginId)

                if (success) {
                    println("✅ Plugin uninstalled successfully!")
                } else {
                    println("❌ Plugin not found or uninstall failed!")
                }
            } catch (e: Exception) {
                println("❌ Error uninstalling plugin: ${e.message}")
            }
        }
    }

    private fun handlePluginEnable(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: plugin enable <plugin-id>")
            return
        }

        val pluginId = args[0]

        // For now, this is equivalent to loading the plugin
        // In a more advanced implementation, we might have enable/disable state
        println("🔄 Loading plugin: $pluginId")

        val discoveryService = PluginDiscoveryService(
            System.getProperty("user.home") + "/.aicodingcli/plugins"
        )
        val availablePlugins = discoveryService.discoverPlugins()
        val pluginInfo = availablePlugins.find { it.metadata.id == pluginId }

        if (pluginInfo == null) {
            println("❌ Plugin not found: $pluginId")
            return
        }

        runBlocking {
            try {
                pluginManager.loadPlugin(pluginInfo.filePath)
                println("✅ Plugin enabled successfully!")
            } catch (e: Exception) {
                println("❌ Error enabling plugin: ${e.message}")
            }
        }
    }

    private fun handlePluginDisable(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: plugin disable <plugin-id>")
            return
        }

        val pluginId = args[0]

        runBlocking {
            try {
                println("⏸️  Disabling plugin: $pluginId")
                val success = pluginManager.unloadPlugin(pluginId)

                if (success) {
                    println("✅ Plugin disabled successfully!")
                } else {
                    println("❌ Plugin not loaded or disable failed!")
                }
            } catch (e: Exception) {
                println("❌ Error disabling plugin: ${e.message}")
            }
        }
    }

    private fun handlePluginInfo(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: plugin info <plugin-id>")
            return
        }

        val pluginId = args[0]

        try {
            // Try to get loaded plugin first
            val loadedPlugin = pluginManager.getPlugin(pluginId)
            if (loadedPlugin != null) {
                displayPluginInfo(loadedPlugin, true)
                return
            }

            // If not loaded, check available plugins
            val discoveryService = PluginDiscoveryService(
                System.getProperty("user.home") + "/.aicodingcli/plugins"
            )
            val availablePlugins = discoveryService.discoverPlugins()
            val pluginInfo = availablePlugins.find { it.metadata.id == pluginId }

            if (pluginInfo != null) {
                displayPluginInfo(pluginInfo.metadata, false, pluginInfo.filePath)
            } else {
                println("❌ Plugin not found: $pluginId")
            }

        } catch (e: Exception) {
            println("❌ Error getting plugin info: ${e.message}")
        }
    }

    private fun displayPluginInfo(plugin: Plugin, isLoaded: Boolean) {
        displayPluginInfo(plugin.metadata, isLoaded)
    }

    private fun displayPluginInfo(metadata: PluginMetadata, isLoaded: Boolean, filePath: String? = null) {
        println("📦 Plugin Information")
        println("=" * 50)
        println("ID: ${metadata.id}")
        println("Name: ${metadata.name}")
        println("Version: ${metadata.version}")
        println("Author: ${metadata.author}")
        println("Description: ${metadata.description}")
        println("Main Class: ${metadata.mainClass}")
        println("Status: ${if (isLoaded) "✅ Loaded" else "📋 Available"}")

        if (filePath != null) {
            println("File: $filePath")
        }

        metadata.website?.let { website ->
            println("Website: $website")
        }

        metadata.minCliVersion?.let { minVersion ->
            println("Min CLI Version: $minVersion")
        }

        if (metadata.dependencies.isNotEmpty()) {
            println("\nDependencies:")
            metadata.dependencies.forEach { dep ->
                val optional = if (dep.optional) " (optional)" else ""
                println("  - ${dep.id} ${dep.version}$optional")
            }
        }

        if (metadata.permissions.isNotEmpty()) {
            println("\nPermissions:")
            metadata.permissions.forEach { permission ->
                when (permission) {
                    is PluginPermission.FileSystemPermission -> {
                        val access = if (permission.readOnly) "read-only" else "read-write"
                        println("  - File System ($access): ${permission.allowedPaths.joinToString(", ")}")
                    }
                    is PluginPermission.NetworkPermission -> {
                        println("  - Network: ${permission.allowedHosts.joinToString(", ")}")
                    }
                    is PluginPermission.SystemPermission -> {
                        println("  - System Commands: ${permission.allowedCommands.joinToString(", ")}")
                    }
                    is PluginPermission.ConfigPermission -> {
                        println("  - Configuration Access")
                    }
                    is PluginPermission.HistoryPermission -> {
                        println("  - History Access")
                    }
                }
            }
        }
    }

    private fun handlePluginValidate(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: plugin validate <plugin-path>")
            return
        }

        val pluginPath = args[0]

        try {
            println("🔍 Validating plugin: $pluginPath")
            val result = pluginManager.validatePlugin(pluginPath)

            if (result.isValid) {
                println("✅ Plugin validation successful!")
            } else {
                println("❌ Plugin validation failed!")
            }

            if (result.errors.isNotEmpty()) {
                println("\n🔴 Errors:")
                result.errors.forEach { error ->
                    println("  - $error")
                }
            }

            if (result.warnings.isNotEmpty()) {
                println("\n🟡 Warnings:")
                result.warnings.forEach { warning ->
                    println("  - $warning")
                }
            }

        } catch (e: Exception) {
            println("❌ Error validating plugin: ${e.message}")
        }
    }

    private fun handleContinuousCommand(args: Array<String>) {
        if (args.isEmpty()) {
            printContinuousHelp()
            return
        }

        when (args[0]) {
            "--list" -> handleContinuousList()
            "--status" -> handleContinuousStatus(args.drop(1).toTypedArray())
            "--continue" -> handleContinuousContinue(args.drop(1).toTypedArray())
            else -> {
                // Treat as requirement for new continuous conversation
                val requirement = args.joinToString(" ")
                handleContinuousExecute(requirement)
            }
        }
    }

    private fun printContinuousHelp() {
        println("""
            Continuous Conversation Commands:

            continuous <requirement>         Start auto-execution with AI guidance
            continuous --list               List all conversation sessions
            continuous --status <session-id> Show session status
            continuous --continue <session-id> Continue paused session

            Examples:
            continuous "Create a User data class with validation"
            continuous "Build a REST API for user management"
            continuous --continue abc123
            continuous --list
            continuous --status abc123

            Features:
            - Automatic task decomposition
            - Intelligent tool selection
            - 25-round execution limit for safety
            - Session pause and resume capability
            - Detailed execution tracking
        """.trimIndent())
    }

    private fun handleContinuousExecute(requirement: String) {
        DebugManager.info("Starting continuous conversation with AI guidance...")
        DebugManager.info("Requirement: $requirement")
        DebugManager.info("AI will automatically decompose and execute tasks")

        if (!DebugManager.isDebugEnabled()) {
            println("🚀 Starting continuous conversation with AI guidance...")
            println("📋 Requirement: $requirement")
            println("🤖 AI will automatically decompose and execute tasks")
            println()
        }

        runBlocking {
            try {
                DebugManager.separator("CONTINUOUS EXECUTION START")

                // Execute the conversation
                val result = autoExecutionEngine.executeConversation(requirement)

                DebugManager.separator("CONTINUOUS EXECUTION END")

                // Display results
                displayExecutionResult(result)

            } catch (e: Exception) {
                DebugManager.error("Error during continuous conversation execution: ${e.message}")
                if (!DebugManager.isDebugEnabled()) {
                    println("❌ Error during continuous conversation execution:")
                    println("   ${e.message}")
                    println()
                    println("💡 This might be due to missing dependencies or configuration issues.")
                    println("🔧 Please ensure all required components are properly initialized.")
                }
            }
        }
    }

    private fun handleContinuousList() {
        println("📋 Listing conversation sessions...")
        println()

        runBlocking {
            try {
                val activeSessions = conversationStateManager.getActiveSessions()

                if (activeSessions.isEmpty()) {
                    println("No active conversation sessions found.")
                    return@runBlocking
                }

                println("Active Sessions:")
                println("=".repeat(60))

                activeSessions.forEach { session ->
                    val date = formatTimestamp(session.updatedAt.epochSecond)
                    val statusIcon = when (session.state.status) {
                        ConversationStatus.CREATED -> "🆕"
                        ConversationStatus.PLANNING -> "📋"
                        ConversationStatus.EXECUTING -> "⚡"
                        ConversationStatus.WAITING_USER -> "⏸️"
                        ConversationStatus.COMPLETED -> "✅"
                        ConversationStatus.FAILED -> "❌"
                        ConversationStatus.CANCELLED -> "🚫"
                    }

                    println("$statusIcon ID: ${session.id.take(8)}")
                    println("   Requirement: ${session.requirement.take(80)}${if (session.requirement.length > 80) "..." else ""}")
                    println("   Status: ${session.state.status}")
                    println("   Tasks: ${session.tasks.size}")
                    println("   Execution Round: ${session.state.executionRound}")
                    println("   Updated: $date")
                    println("-".repeat(40))
                }

            } catch (e: Exception) {
                println("❌ Error listing sessions: ${e.message}")
            }
        }
    }

    private fun handleContinuousStatus(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: continuous --status <session-id>")
            return
        }

        val sessionId = args[0]
        println("📊 Checking status for session: $sessionId")
        println()

        runBlocking {
            try {
                val session = conversationStateManager.getSession(sessionId)

                if (session == null) {
                    println("❌ Session not found: $sessionId")
                    return@runBlocking
                }

                displaySessionDetails(session)

            } catch (e: Exception) {
                println("❌ Error checking session status: ${e.message}")
            }
        }
    }

    private fun handleContinuousContinue(args: Array<String>) {
        if (args.isEmpty()) {
            println("Usage: continuous --continue <session-id>")
            return
        }

        val sessionId = args[0]
        println("🔄 Continuing session: $sessionId")
        println()

        runBlocking {
            try {
                // Check if session exists
                val session = conversationStateManager.getSession(sessionId)
                if (session == null) {
                    println("❌ Session not found: $sessionId")
                    return@runBlocking
                }

                // Continue the conversation
                val result = autoExecutionEngine.continueConversation(sessionId)

                // Display results
                displayExecutionResult(result)

            } catch (e: Exception) {
                println("❌ Error continuing session: ${e.message}")
            }
        }
    }

    private fun displayExecutionResult(result: ExecutionResult) {
        if (result.success) {
            println("✅ Execution completed successfully!")
            println("📋 Session ID: ${result.sessionId?.take(8)}")
            println("📊 Final Status: ${result.finalStatus}")
            println("🔄 Execution Rounds: ${result.executionRounds}")
            println("⏱️  Execution Time: ${result.executionTime}")
            println("📝 Executed Steps: ${result.executedSteps.size}")

            if (result.summary != null) {
                println()
                println("📄 Summary:")
                println(result.summary)
            }

            if (result.executedSteps.isNotEmpty()) {
                println()
                println("🔧 Executed Steps:")
                result.executedSteps.forEachIndexed { index, step ->
                    DebugManager.logExecutionStep(
                        stepNumber = index + 1,
                        toolName = step.toolCall.toolName,
                        success = step.result.success,
                        output = step.result.output
                    )

                    if (!DebugManager.isDebugEnabled()) {
                        val status = if (step.result.success) "✅" else "❌"
                        println("  ${index + 1}. $status ${step.toolCall.toolName}")
                        if (!step.result.success && step.result.error != null) {
                            println("     Error: ${step.result.error}")
                        }
                    }
                }
            }

            if (result.finalStatus == ConversationStatus.WAITING_USER) {
                println()
                println("⏸️  Execution paused. Use 'continuous --continue ${result.sessionId?.take(8)}' to resume.")
            }
        } else {
            println("❌ Execution failed!")
            println("📋 Session ID: ${result.sessionId?.take(8) ?: "unknown"}")
            println("📊 Final Status: ${result.finalStatus}")
            println("🔄 Execution Rounds: ${result.executionRounds}")
            println("⏱️  Execution Time: ${result.executionTime}")

            if (result.error != null) {
                println()
                println("🔴 Error:")
                println(result.error)
            }
        }
    }

    private fun displaySessionDetails(session: ConversationSession) {
        val statusIcon = when (session.state.status) {
            ConversationStatus.CREATED -> "🆕"
            ConversationStatus.PLANNING -> "📋"
            ConversationStatus.EXECUTING -> "⚡"
            ConversationStatus.WAITING_USER -> "⏸️"
            ConversationStatus.COMPLETED -> "✅"
            ConversationStatus.FAILED -> "❌"
            ConversationStatus.CANCELLED -> "🚫"
        }

        println("$statusIcon Session Details")
        println("=".repeat(60))
        println("ID: ${session.id}")
        println("Requirement: ${session.requirement}")
        println("Status: ${session.state.status}")
        println("Current Task Index: ${session.state.currentTaskIndex}")
        println("Execution Round: ${session.state.executionRound}")
        println("Created: ${formatTimestamp(session.createdAt.epochSecond)}")
        println("Updated: ${formatTimestamp(session.updatedAt.epochSecond)}")

        if (session.tasks.isNotEmpty()) {
            println()
            println("📋 Tasks (${session.tasks.size}):")
            session.tasks.forEachIndexed { index, task ->
                val current = if (index == session.state.currentTaskIndex) "👉" else "  "
                println("$current ${index + 1}. ${task.description}")
                println("     Tools: ${task.toolCalls.map { it.toolName }.joinToString(", ")}")
            }
        }

        if (session.executionHistory.isNotEmpty()) {
            println()
            println("🔧 Execution History (${session.executionHistory.size}):")
            session.executionHistory.takeLast(5).forEach { step ->
                val status = if (step.result.success) "✅" else "❌"
                println("  $status ${step.toolCall.toolName} (${step.duration})")
                if (!step.result.success && step.result.error != null) {
                    println("     Error: ${step.result.error}")
                }
            }
            if (session.executionHistory.size > 5) {
                println("  ... and ${session.executionHistory.size - 5} more steps")
            }
        }

        if (session.state.errors.isNotEmpty()) {
            println()
            println("🔴 Errors:")
            session.state.errors.forEach { error ->
                println("  - ${error.message} (${error.code})")
            }
        }
    }
}
