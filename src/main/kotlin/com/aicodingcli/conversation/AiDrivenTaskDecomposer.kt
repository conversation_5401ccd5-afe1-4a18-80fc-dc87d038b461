package com.aicodingcli.conversation

import com.aicodingcli.ai.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * AI-driven task decomposer that uses AI to analyze requirements and generate tasks
 */
class AiDrivenTaskDecomposer(
    private val aiService: AiService,
    private val availableTools: List<ToolMetadata>,
    private val json: Json = Json { ignoreUnknownKeys = true }
) : TaskDecomposer {
    
    override suspend fun decompose(requirement: String, context: ProjectContext): List<ExecutableTask> {
        if (requirement.isBlank()) {
            return emptyList()
        }
        
        try {
            val prompt = buildTaskDecompositionPrompt(requirement, context, availableTools)
            
            val response = aiService.chat(AiRequest(
                messages = listOf(AiMessage(MessageRole.USER, prompt)),
                model = aiService.config.model,
                temperature = aiService.config.temperature,
                maxTokens = aiService.config.maxTokens
            ))
            
            return parseTaskDecompositionResponse(response.content)
            
        } catch (e: Exception) {
            // Fallback to simple task if AI fails
            return createFallbackTask(requirement, context)
        }
    }
    
    override suspend fun refineTask(task: ExecutableTask, feedback: String): ExecutableTask {
        try {
            val prompt = buildTaskRefinementPrompt(task, feedback, availableTools)
            
            val response = aiService.chat(AiRequest(
                messages = listOf(AiMessage(MessageRole.USER, prompt)),
                model = aiService.config.model,
                temperature = aiService.config.temperature,
                maxTokens = aiService.config.maxTokens
            ))
            
            return parseTaskRefinementResponse(response.content, task)
            
        } catch (e: Exception) {
            // Fallback to simple refinement
            return task.copy(description = "${task.description} (refined with: $feedback)")
        }
    }
    
    override suspend fun validateTaskSequence(tasks: List<ExecutableTask>): ValidationResult {
        if (tasks.isEmpty()) {
            return ValidationResult.valid()
        }
        
        val errors = mutableListOf<String>()
        val taskIds = tasks.map { it.id }.toSet()
        
        // Check for circular dependencies
        if (hasCircularDependencies(tasks)) {
            errors.add("Circular dependency detected in task sequence")
        }
        
        // Check for missing dependencies
        tasks.forEach { task ->
            task.dependencies.forEach { depId ->
                if (depId !in taskIds) {
                    errors.add("Task ${task.id} depends on non-existent task $depId")
                }
            }
        }
        
        return if (errors.isEmpty()) {
            ValidationResult.valid()
        } else {
            ValidationResult.invalid(errors)
        }
    }
    
    private fun buildTaskDecompositionPrompt(
        requirement: String, 
        context: ProjectContext, 
        tools: List<ToolMetadata>
    ): String {
        val toolsDescription = tools.joinToString("\n") { tool ->
            "- ${tool.name}: ${tool.description}"
        }
        
        return """
            You are an expert software architect and project manager. Your task is to analyze a user requirement and decompose it into executable tasks using available tools.
            
            **User Requirement**: $requirement
            
            **Project Context**:
            - Project Path: ${context.projectPath}
            - Language: ${context.language}
            - Framework: ${context.framework ?: "None"}
            - Build Tool: ${context.buildTool ?: "None"}
            
            **Available Tools**:
            $toolsDescription
            
            **Instructions**:
            1. Analyze the requirement carefully to understand the user's intent
            2. Break down the requirement into logical, executable tasks
            3. For each task, specify the appropriate tool calls with correct parameters
            4. Consider dependencies between tasks (e.g., create model before service)
            5. Assign appropriate priorities (1 = highest priority)
            6. Generate realistic file paths based on the project context
            7. Create meaningful, implementable code content
            
            **Response Format** (JSON):
            {
                "analysis": {
                    "intent": "What the user wants to achieve",
                    "complexity": "LOW|MEDIUM|HIGH",
                    "estimatedTasks": "number",
                    "requiredTools": ["tool1", "tool2"]
                },
                "tasks": [
                    {
                        "id": "unique-task-id",
                        "description": "Clear description of what this task does",
                        "toolCalls": [
                            {
                                "toolName": "tool-name",
                                "parameters": {
                                    "param1": "value1",
                                    "param2": "value2"
                                }
                            }
                        ],
                        "dependencies": ["task-id-1", "task-id-2"],
                        "priority": 1,
                        "estimatedDuration": "PT5M"
                    }
                ]
            }
            
            **Examples of Good Task Decomposition**:
            - For "Create a User class": Generate actual Kotlin class with properties, not just TODO
            - For "REST API": Create Model, Service, Controller with proper Spring Boot annotations
            - For "Database setup": Create entity, repository, configuration files
            
            Respond only with valid JSON.
        """.trimIndent()
    }
    
    private fun buildTaskRefinementPrompt(
        task: ExecutableTask, 
        feedback: String, 
        tools: List<ToolMetadata>
    ): String {
        val toolsDescription = tools.joinToString("\n") { tool ->
            "- ${tool.name}: ${tool.description}"
        }
        
        return """
            You are refining an existing task based on user feedback.
            
            **Current Task**:
            - ID: ${task.id}
            - Description: ${task.description}
            - Tool Calls: ${task.toolCalls.map { "${it.toolName}(${it.parameters.keys.joinToString()})" }}
            
            **User Feedback**: $feedback
            
            **Available Tools**:
            $toolsDescription
            
            **Instructions**:
            1. Analyze the feedback and understand what needs to be changed
            2. Modify the task description and tool calls accordingly
            3. Keep the same task ID and basic structure
            4. Ensure the refined task still accomplishes the original goal
            
            **Response Format** (JSON):
            {
                "refinedTask": {
                    "id": "${task.id}",
                    "description": "Updated description incorporating feedback",
                    "toolCalls": [
                        {
                            "toolName": "tool-name",
                            "parameters": {
                                "param1": "updated-value1",
                                "param2": "updated-value2"
                            }
                        }
                    ],
                    "dependencies": ${task.dependencies},
                    "priority": ${task.priority}
                }
            }
            
            Respond only with valid JSON.
        """.trimIndent()
    }
    
    private fun parseTaskDecompositionResponse(response: String): List<ExecutableTask> {
        return try {
            val jsonResponse = json.decodeFromString<TaskDecompositionResponse>(response)
            
            jsonResponse.tasks.map { taskJson ->
                ExecutableTask(
                    id = taskJson.id,
                    description = taskJson.description,
                    toolCalls = taskJson.toolCalls.map { toolCallJson ->
                        ToolCall(
                            toolName = toolCallJson.toolName,
                            parameters = toolCallJson.parameters
                        )
                    },
                    dependencies = taskJson.dependencies,
                    priority = taskJson.priority,
                    estimatedDuration = taskJson.estimatedDuration?.let { 
                        kotlin.time.Duration.parse(it) 
                    }
                )
            }
        } catch (e: Exception) {
            // If parsing fails, return empty list
            emptyList()
        }
    }
    
    private fun parseTaskRefinementResponse(response: String, originalTask: ExecutableTask): ExecutableTask {
        return try {
            val jsonResponse = json.decodeFromString<TaskRefinementResponse>(response)
            val refinedTask = jsonResponse.refinedTask
            
            ExecutableTask(
                id = refinedTask.id,
                description = refinedTask.description,
                toolCalls = refinedTask.toolCalls.map { toolCallJson ->
                    ToolCall(
                        toolName = toolCallJson.toolName,
                        parameters = toolCallJson.parameters
                    )
                },
                dependencies = refinedTask.dependencies,
                priority = refinedTask.priority,
                estimatedDuration = originalTask.estimatedDuration
            )
        } catch (e: Exception) {
            // If parsing fails, return original task
            originalTask
        }
    }
    
    private fun createFallbackTask(requirement: String, context: ProjectContext): List<ExecutableTask> {
        val filePath = if (context.projectPath.isBlank()) {
            "Implementation.kt"
        } else {
            "${context.projectPath}/src/main/kotlin/Implementation.kt"
        }
        
        return listOf(
            ExecutableTask(
                description = "Implement requirement: $requirement",
                toolCalls = listOf(
                    ToolCall(
                        toolName = "save-file",
                        parameters = mapOf(
                            "path" to filePath,
                            "file_content" to "// TODO: Implement $requirement\n// AI task decomposition failed, manual implementation needed"
                        )
                    )
                ),
                priority = 1
            )
        )
    }
    
    private fun hasCircularDependencies(tasks: List<ExecutableTask>): Boolean {
        val graph = tasks.associate { it.id to it.dependencies }
        val visited = mutableSetOf<String>()
        val recursionStack = mutableSetOf<String>()
        
        fun hasCycle(taskId: String): Boolean {
            if (recursionStack.contains(taskId)) return true
            if (visited.contains(taskId)) return false
            
            visited.add(taskId)
            recursionStack.add(taskId)
            
            graph[taskId]?.forEach { dependency ->
                if (hasCycle(dependency)) return true
            }
            
            recursionStack.remove(taskId)
            return false
        }
        
        return tasks.any { hasCycle(it.id) }
    }
}

// JSON response models
@Serializable
private data class TaskDecompositionResponse(
    val analysis: AnalysisJson,
    val tasks: List<TaskJson>
)

@Serializable
private data class AnalysisJson(
    val intent: String,
    val complexity: String,
    val estimatedTasks: String,
    val requiredTools: List<String>
)

@Serializable
private data class TaskJson(
    val id: String,
    val description: String,
    val toolCalls: List<ToolCallJson>,
    val dependencies: List<String>,
    val priority: Int,
    val estimatedDuration: String? = null
)

@Serializable
private data class ToolCallJson(
    val toolName: String,
    val parameters: Map<String, String>
)

@Serializable
private data class TaskRefinementResponse(
    val refinedTask: TaskJson
)
