package com.aicodingcli.conversation

import com.aicodingcli.ai.AiMessage
import com.aicodingcli.ai.MessageRole
import com.aicodingcli.debug.DebugManager

/**
 * Manages conversation history for multi-turn AI interactions
 */
class ConversationHistoryManager {
    
    private val messages = mutableListOf<AiMessage>()
    private var systemPrompt: String? = null
    
    /**
     * Set the initial system prompt
     */
    fun setSystemPrompt(prompt: String) {
        systemPrompt = prompt
        DebugManager.debug("System prompt set (${prompt.length} characters)")
    }
    
    /**
     * Add a user message to the conversation
     */
    fun addUserMessage(content: String) {
        messages.add(AiMessage(MessageRole.USER, content))
        DebugManager.debug("Added user message (${content.length} characters)")
    }
    
    /**
     * Add an assistant message to the conversation
     */
    fun addAssistantMessage(content: String) {
        messages.add(AiMessage(MessageRole.ASSISTANT, content))
        DebugManager.debug("Added assistant message (${content.length} characters)")
    }
    
    /**
     * Add a tool result as a user message
     */
    fun addToolResult(toolName: String, result: ToolResult) {
        val toolResultMessage = buildToolResultMessage(toolName, result)
        messages.add(AiMessage(MessageRole.USER, toolResultMessage))
        DebugManager.debug("Added tool result for $toolName (success: ${result.success})")
    }
    
    /**
     * Get all messages for the current conversation
     */
    fun getAllMessages(): List<AiMessage> {
        val allMessages = mutableListOf<AiMessage>()
        
        // Add system prompt if exists
        systemPrompt?.let { 
            allMessages.add(AiMessage(MessageRole.SYSTEM, it))
        }
        
        // Add conversation messages
        allMessages.addAll(messages)
        
        return allMessages
    }
    
    /**
     * Get recent messages (for token limit management)
     */
    fun getRecentMessages(maxMessages: Int = 10): List<AiMessage> {
        val allMessages = getAllMessages()
        
        // Always include system prompt if exists
        val systemMessage = allMessages.firstOrNull { it.role == MessageRole.SYSTEM }
        val conversationMessages = allMessages.filter { it.role != MessageRole.SYSTEM }
        
        val recentConversation = if (conversationMessages.size <= maxMessages) {
            conversationMessages
        } else {
            conversationMessages.takeLast(maxMessages)
        }
        
        return listOfNotNull(systemMessage) + recentConversation
    }
    
    /**
     * Clear conversation history but keep system prompt
     */
    fun clearConversation() {
        messages.clear()
        DebugManager.debug("Conversation history cleared")
    }
    
    /**
     * Clear everything including system prompt
     */
    fun clearAll() {
        messages.clear()
        systemPrompt = null
        DebugManager.debug("All conversation data cleared")
    }
    
    /**
     * Get conversation statistics
     */
    fun getStats(): ConversationStats {
        val totalMessages = messages.size
        val userMessages = messages.count { it.role == MessageRole.USER }
        val assistantMessages = messages.count { it.role == MessageRole.ASSISTANT }
        val totalTokens = messages.sumOf { estimateTokens(it.content) }
        
        return ConversationStats(
            totalMessages = totalMessages,
            userMessages = userMessages,
            assistantMessages = assistantMessages,
            estimatedTokens = totalTokens,
            hasSystemPrompt = systemPrompt != null
        )
    }
    
    /**
     * Build a formatted tool result message
     */
    private fun buildToolResultMessage(toolName: String, result: ToolResult): String {
        return if (result.success) {
            """
            Tool execution completed successfully:
            
            **Tool**: $toolName
            **Status**: SUCCESS
            **Output**:
            ${result.output}
            """.trimIndent()
        } else {
            """
            Tool execution failed:
            
            **Tool**: $toolName
            **Status**: FAILED
            **Error**: ${result.error ?: "Unknown error"}
            **Output**: ${result.output}
            """.trimIndent()
        }
    }
    
    /**
     * Simple token estimation (rough approximation)
     */
    private fun estimateTokens(text: String): Int {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return (text.length / 4).coerceAtLeast(1)
    }
}

/**
 * Conversation statistics
 */
data class ConversationStats(
    val totalMessages: Int,
    val userMessages: Int,
    val assistantMessages: Int,
    val estimatedTokens: Int,
    val hasSystemPrompt: Boolean
)

/**
 * Conversation history builder for specific use cases
 */
object ConversationHistoryBuilder {
    
    /**
     * Create initial system prompt for AI-driven execution
     */
    fun buildInitialSystemPrompt(
        requirement: String,
        context: ExecutionContext,
        availableTools: List<ToolMetadata>
    ): String {
        val currentTime = java.time.LocalDateTime.now().toString()
        val workingDir = context.workingDirectory
        val projectPath = if (context.projectPath.isBlank()) workingDir else context.projectPath
        
        // Get directory structure
        val directoryStructure = try {
            val dir = java.io.File(projectPath)
            if (dir.exists() && dir.isDirectory()) {
                dir.listFiles()?.take(10)?.joinToString("\n") { file ->
                    val type = if (file.isDirectory()) "[DIR]" else "[FILE]"
                    "  $type ${file.name}"
                } ?: "Empty directory"
            } else {
                "Directory does not exist or is not accessible"
            }
        } catch (e: Exception) {
            "Unable to read directory structure: ${e.message}"
        }
        
        val toolsText = availableTools.joinToString("\n") { tool ->
            val parametersText = if (tool.parameters.isNotEmpty()) {
                val params = tool.parameters.joinToString(", ") { param ->
                    val requiredText = if (param.required) "required" else "optional"
                    "${param.name} (${param.type}, $requiredText): ${param.description}"
                }
                "\n  Parameters: $params"
            } else {
                ""
            }
            "- ${tool.name}: ${tool.description}$parametersText"
        }
        
        return """
            You are an AI assistant that helps complete programming requirements through tool execution.
            
            **Original Requirement**: $requirement
            
            **System Environment**:
            - Current Time: $currentTime
            - Working Directory: $workingDir
            - Project Path: $projectPath
            - Language: ${context.language}
            - Framework: ${context.framework}
            - Build Tool: ${context.buildTool}
            
            **Current Directory Structure** ($projectPath):
            $directoryStructure
            
            **Available Tools**:
            $toolsText
            
            **Instructions**:
            - Analyze the requirement and execute appropriate tools to complete it
            - When I provide tool execution results, use them to decide the next action
            - Respond in JSON format with your next action decision
            - Be efficient and avoid unnecessary tool calls
            
            **Response Format**:
            {
                "action": "EXECUTE_TOOL|COMPLETE|WAIT_USER|FAIL",
                "toolName": "string (if action is EXECUTE_TOOL)",
                "parameters": {"key": "value"} (if action is EXECUTE_TOOL),
                "reasoning": "string explaining your decision",
                "confidence": number (0.0 to 1.0)
            }
        """.trimIndent()
    }
    
    /**
     * Create a simple action request message
     */
    fun buildActionRequestMessage(): String {
        return "Based on the current situation, what should be the next action?"
    }
}
