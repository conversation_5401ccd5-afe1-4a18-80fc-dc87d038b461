package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.*
import java.io.File

/**
 * <PERSON><PERSON> for save-file tool
 */
class SaveFileHandler : BaseToolHandler("save-file") {
    
    override suspend fun executeInternal(parameters: Map<String, String>, workingDirectory: String): ToolResult {
        val path = parameters["path"]!!
        val content = parameters["file_content"]!!
        
        // Validate path
        if (path.contains('\u0000')) {
            return ToolResult.failure("Invalid file path: $path")
        }

        // Handle both absolute and relative paths
        val file = if (File(path).isAbsolute) {
            File(path)
        } else {
            File(workingDirectory, path)
        }
        
        // Try to create parent directories
        val parentDir = file.parentFile
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                return ToolResult.failure("Failed to create parent directories for: $path")
            }
        }
        
        file.writeText(content)
        
        return ToolResult.success(
            output = "File saved successfully: $path",
            metadata = mapOf("file_size" to file.length().toString())
        )
    }
    
    override fun validateParameters(parameters: Map<String, String>): ValidationResult {
        val errors = checkRequiredParameters(parameters, listOf("path", "file_content")).toMutableList()

        val path = parameters["path"]
        if (path.isNullOrBlank()) {
            errors.add("Path cannot be empty")
        }

        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)
    }
    
    override fun getMetadata(): ToolMetadata {
        return ToolMetadata(
            name = toolName,
            description = "Create a new file with content",
            parameters = listOf(
                ToolParameter("path", "string", "File path relative to working directory", required = true),
                ToolParameter("file_content", "string", "Content to write to the file", required = true)
            ),
            category = "file"
        )
    }
}

/**
 * Handler for view tool
 */
class ViewHandler : BaseToolHandler("view") {
    
    override suspend fun executeInternal(parameters: Map<String, String>, workingDirectory: String): ToolResult {
        val path = parameters["path"]!!
        val explicitType = parameters["type"]

        // Handle both absolute and relative paths
        val file = if (File(path).isAbsolute) {
            File(path)
        } else {
            File(workingDirectory, path)
        }

        if (!file.exists()) {
            return ToolResult.failure("File or directory does not exist: $path")
        }

        // Auto-detect type if not provided
        val type = explicitType ?: if (file.isFile) "file" else "directory"

        val output = when (type) {
            "file" -> {
                if (file.isFile) {
                    try {
                        val content = file.readText()
                        if (content.length > 10000) {
                            // For large files, show first 5000 chars with truncation notice
                            content.take(5000) + "\n\n... [File truncated - showing first 5000 characters of ${content.length} total]"
                        } else {
                            content
                        }
                    } catch (e: Exception) {
                        "Error reading file: ${e.message}"
                    }
                } else {
                    "Path is not a file: $path"
                }
            }
            "directory" -> {
                if (file.isDirectory) {
                    try {
                        val files = file.listFiles()
                        if (files == null) {
                            "Cannot read directory contents: $path"
                        } else if (files.isEmpty()) {
                            "Empty directory"
                        } else {
                            files.sortedWith(compareBy({ !it.isDirectory() }, { it.name }))
                                .joinToString("\n") {
                                    val fileType = if (it.isDirectory()) "[DIR]" else "[FILE]"
                                    val size = if (it.isFile()) " (${it.length()} bytes)" else ""
                                    "$fileType ${it.name}$size"
                                }
                        }
                    } catch (e: Exception) {
                        "Error reading directory: ${e.message}"
                    }
                } else {
                    "Path is not a directory: $path"
                }
            }
            else -> "Invalid type: $type. Use 'file' or 'directory'"
        }

        return ToolResult.success(output)
    }
    
    override fun validateParameters(parameters: Map<String, String>): ValidationResult {
        val errors = checkRequiredParameters(parameters, listOf("path")).toMutableList()

        val type = parameters["type"]
        if (type != null && type !in listOf("file", "directory")) {
            errors.add("Type must be 'file' or 'directory' (or omit for auto-detection)")
        }

        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)
    }
    
    override fun getMetadata(): ToolMetadata {
        return ToolMetadata(
            name = toolName,
            description = "View file or directory contents. Supports both absolute and relative paths. Type is auto-detected if not specified.",
            parameters = listOf(
                ToolParameter("path", "string", "Path to view (absolute or relative)", required = true),
                ToolParameter("type", "string", "Type: 'file' or 'directory' (auto-detected if omitted)", required = false)
            ),
            category = "file",
            examples = listOf(
                ToolExample(
                    description = "View a file with auto-detection",
                    parameters = mapOf("path" to "src/main/kotlin/Main.kt"),
                    expectedResult = "File contents"
                ),
                ToolExample(
                    description = "View a directory",
                    parameters = mapOf("path" to "src", "type" to "directory"),
                    expectedResult = "Directory listing"
                )
            )
        )
    }
}

/**
 * Handler for str-replace-editor tool
 */
class StrReplaceEditorHandler : BaseToolHandler("str-replace-editor") {
    
    override suspend fun executeInternal(parameters: Map<String, String>, workingDirectory: String): ToolResult {
        val path = parameters["path"]!!
        val oldStr = parameters["old_str"]!!
        val newStr = parameters["new_str"]!!
        
        // Handle both absolute and relative paths
        val file = if (File(path).isAbsolute) {
            File(path)
        } else {
            File(workingDirectory, path)
        }
        
        if (!file.exists()) {
            return ToolResult.failure("File does not exist: $path")
        }
        
        val content = file.readText()
        if (!content.contains(oldStr)) {
            return ToolResult.failure("Text to replace not found: $oldStr")
        }
        
        val newContent = content.replace(oldStr, newStr)
        file.writeText(newContent)
        
        return ToolResult.success(
            output = "Text replaced successfully in $path",
            metadata = mapOf("replacements" to "1")
        )
    }
    
    override fun validateParameters(parameters: Map<String, String>): ValidationResult {
        val errors = checkRequiredParameters(parameters, listOf("path", "old_str", "new_str"))
        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)
    }
    
    override fun getMetadata(): ToolMetadata {
        return ToolMetadata(
            name = toolName,
            description = "Replace text in a file",
            parameters = listOf(
                ToolParameter("path", "string", "File path", required = true),
                ToolParameter("old_str", "string", "Text to replace", required = true),
                ToolParameter("new_str", "string", "Replacement text", required = true)
            ),
            category = "file"
        )
    }
}

/**
 * Handler for remove-files tool
 */
class RemoveFilesHandler : BaseToolHandler("remove-files") {
    
    override suspend fun executeInternal(parameters: Map<String, String>, workingDirectory: String): ToolResult {
        val filePaths = parameters["file_paths"]!!
        val paths = filePaths.split(",").map { it.trim() }
        val removedFiles = mutableListOf<String>()
        
        paths.forEach { path ->
            // Handle both absolute and relative paths
            val file = if (File(path).isAbsolute) {
                File(path)
            } else {
                File(workingDirectory, path)
            }
            if (file.exists() && file.delete()) {
                removedFiles.add(path)
            }
        }
        
        return ToolResult.success(
            output = "Removed ${removedFiles.size} files: ${removedFiles.joinToString(", ")}",
            metadata = mapOf("removed_count" to removedFiles.size.toString())
        )
    }
    
    override fun validateParameters(parameters: Map<String, String>): ValidationResult {
        val errors = checkRequiredParameters(parameters, listOf("file_paths"))
        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)
    }
    
    override fun getMetadata(): ToolMetadata {
        return ToolMetadata(
            name = toolName,
            description = "Remove files from filesystem",
            parameters = listOf(
                ToolParameter("file_paths", "array", "Array of file paths to remove", required = true)
            ),
            category = "file"
        )
    }
}
