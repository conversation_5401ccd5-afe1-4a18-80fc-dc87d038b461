package com.aicodingcli.debug

import com.aicodingcli.ai.AiRequest
import com.aicodingcli.ai.AiResponse
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Debug manager for controlling debug output and AI conversation logging
 */
object DebugManager {
    
    private var debugEnabled = false
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss.SSS")
    
    /**
     * Enable or disable debug mode
     */
    fun setDebugMode(enabled: Boolean) {
        debugEnabled = enabled
        if (enabled) {
            println("🐛 Debug mode enabled - AI conversations will be logged")
        } else {
            println("✅ Debug mode disabled - Only essential information will be shown")
        }
    }
    
    /**
     * Check if debug mode is enabled
     */
    fun isDebugEnabled(): Boolean = debugEnabled
    
    /**
     * Print debug message only if debug mode is enabled
     */
    fun debug(message: String) {
        if (debugEnabled) {
            val timestamp = LocalDateTime.now().format(timeFormatter)
            println("🐛 [$timestamp] $message")
        }
    }
    
    /**
     * Print info message (always shown)
     */
    fun info(message: String) {
        println("ℹ️  $message")
    }
    
    /**
     * Print success message (always shown)
     */
    fun success(message: String) {
        println("✅ $message")
    }
    
    /**
     * Print warning message (always shown)
     */
    fun warning(message: String) {
        println("⚠️  $message")
    }
    
    /**
     * Print error message (always shown)
     */
    fun error(message: String) {
        println("❌ $message")
    }
    
    /**
     * Log AI request (only in debug mode)
     */
    fun logAiRequest(request: AiRequest, context: String = "") {
        if (!debugEnabled) return
        
        val timestamp = LocalDateTime.now().format(timeFormatter)
        println("\n🤖 [$timestamp] AI REQUEST ${if (context.isNotBlank()) "($context)" else ""}")
        println("📤 Model: ${request.model}")
        println("📤 Temperature: ${request.temperature}")
        println("📤 Max Tokens: ${request.maxTokens}")
        println("📤 Messages:")
        request.messages.forEachIndexed { index, message ->
            println("   ${index + 1}. [${message.role}] ${message.content.take(200)}${if (message.content.length > 200) "..." else ""}")
        }
        
        if (request.messages.any { it.content.length > 200 }) {
            println("\n📤 Full Prompt:")
            println("=" * 80)
            request.messages.forEach { message ->
                println("[${message.role.name}]")
                println(message.content)
                println("-" * 40)
            }
            println("=" * 80)
        }
    }
    
    /**
     * Log AI response (only in debug mode)
     */
    fun logAiResponse(response: AiResponse, context: String = "") {
        if (!debugEnabled) return
        
        val timestamp = LocalDateTime.now().format(timeFormatter)
        println("\n🤖 [$timestamp] AI RESPONSE ${if (context.isNotBlank()) "($context)" else ""}")
        println("📥 Model: ${response.model}")
        println("📥 Finish Reason: ${response.finishReason}")
        println("📥 Usage: ${response.usage.promptTokens}+${response.usage.completionTokens}=${response.usage.totalTokens} tokens")
        println("📥 Content:")
        println("-" * 40)
        println(response.content)
        println("-" * 40)
    }
    
    /**
     * Log AI conversation (request + response)
     */
    fun logAiConversation(request: AiRequest, response: AiResponse, context: String = "") {
        logAiRequest(request, context)
        logAiResponse(response, context)
    }
    
    /**
     * Log execution step
     */
    fun logExecutionStep(stepNumber: Int, toolName: String, success: Boolean, output: String) {
        if (debugEnabled) {
            val timestamp = LocalDateTime.now().format(timeFormatter)
            val status = if (success) "✅" else "❌"
            println("\n🔧 [$timestamp] EXECUTION STEP $stepNumber")
            println("🔧 Tool: $toolName")
            println("🔧 Status: $status ${if (success) "SUCCESS" else "FAILED"}")
            println("🔧 Output: ${output.take(200)}${if (output.length > 200) "..." else ""}")
            
            if (output.length > 200) {
                println("\n🔧 Full Output:")
                println("-" * 40)
                println(output)
                println("-" * 40)
            }
        } else {
            // In non-debug mode, show minimal info
            val status = if (success) "✅" else "❌"
            println("  $stepNumber. $status $toolName")
        }
    }
    
    /**
     * Log session information
     */
    fun logSessionInfo(sessionId: String, requirement: String, status: String) {
        if (debugEnabled) {
            val timestamp = LocalDateTime.now().format(timeFormatter)
            println("\n📋 [$timestamp] SESSION INFO")
            println("📋 Session ID: $sessionId")
            println("📋 Requirement: $requirement")
            println("📋 Status: $status")
        }
    }
    
    /**
     * Create a separator line for debug output
     */
    fun separator(title: String = "") {
        if (debugEnabled) {
            if (title.isNotBlank()) {
                println("\n" + "=" * 20 + " $title " + "=" * 20)
            } else {
                println("\n" + "=" * 60)
            }
        }
    }
}

/**
 * Extension function for String repetition
 */
private operator fun String.times(count: Int): String {
    return this.repeat(count)
}
