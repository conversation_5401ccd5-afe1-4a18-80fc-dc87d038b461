package com.aicodingcli.interaction

import com.aicodingcli.debug.DebugManager
import java.util.Scanner

/**
 * Manager for handling user interactions during AI-driven execution
 */
class UserInteractionManager {
    
    private val scanner = Scanner(System.`in`)
    
    /**
     * Prompt user for input with a question
     */
    fun promptUser(question: String, defaultValue: String? = null): String {
        val prompt = if (defaultValue != null) {
            "$question (default: $defaultValue): "
        } else {
            "$question: "
        }
        
        print("💬 $prompt")
        val input = scanner.nextLine().trim()
        
        return if (input.isBlank() && defaultValue != null) {
            DebugManager.debug("User input was empty, using default: $defaultValue")
            defaultValue
        } else {
            DebugManager.debug("User input received: $input")
            input
        }
    }
    
    /**
     * Prompt user to choose from a list of options
     */
    fun promptChoice(question: String, options: List<String>, defaultIndex: Int? = null): String {
        println("💬 $question")
        options.forEachIndexed { index, option ->
            val marker = if (defaultIndex == index) " (default)" else ""
            println("  ${index + 1}. $option$marker")
        }
        
        while (true) {
            print("💬 Enter your choice (1-${options.size}): ")
            val input = scanner.nextLine().trim()
            
            if (input.isBlank() && defaultIndex != null) {
                DebugManager.debug("User input was empty, using default option: ${options[defaultIndex]}")
                return options[defaultIndex]
            }
            
            try {
                val choice = input.toInt()
                if (choice in 1..options.size) {
                    val selectedOption = options[choice - 1]
                    DebugManager.debug("User selected option $choice: $selectedOption")
                    return selectedOption
                } else {
                    println("❌ Invalid choice. Please enter a number between 1 and ${options.size}")
                }
            } catch (e: NumberFormatException) {
                println("❌ Invalid input. Please enter a number")
            }
        }
    }
    
    /**
     * Prompt user for yes/no confirmation
     */
    fun promptConfirmation(question: String, defaultValue: Boolean? = null): Boolean {
        val defaultText = when (defaultValue) {
            true -> " (Y/n)"
            false -> " (y/N)"
            null -> " (y/n)"
        }
        
        while (true) {
            print("💬 $question$defaultText: ")
            val input = scanner.nextLine().trim().lowercase()
            
            when {
                input.isBlank() && defaultValue != null -> {
                    DebugManager.debug("User input was empty, using default: $defaultValue")
                    return defaultValue
                }
                input in listOf("y", "yes", "true", "1") -> {
                    DebugManager.debug("User confirmed: true")
                    return true
                }
                input in listOf("n", "no", "false", "0") -> {
                    DebugManager.debug("User confirmed: false")
                    return false
                }
                else -> {
                    println("❌ Please enter 'y' for yes or 'n' for no")
                }
            }
        }
    }
    
    /**
     * Prompt user for file path with validation
     */
    fun promptFilePath(question: String, mustExist: Boolean = false, defaultValue: String? = null): String {
        while (true) {
            val path = promptUser(question, defaultValue)
            
            if (path.isBlank()) {
                println("❌ File path cannot be empty")
                continue
            }
            
            if (mustExist) {
                val file = java.io.File(path)
                if (!file.exists()) {
                    println("❌ File does not exist: $path")
                    continue
                }
            }
            
            return path
        }
    }
    
    /**
     * Prompt user for multiple inputs
     */
    fun promptMultipleInputs(prompts: List<Pair<String, String?>>): Map<String, String> {
        val results = mutableMapOf<String, String>()
        
        prompts.forEach { (question, defaultValue) ->
            val key = question.lowercase().replace(Regex("[^a-z0-9]"), "_")
            results[key] = promptUser(question, defaultValue)
        }
        
        return results
    }
    
    /**
     * Display information and wait for user to continue
     */
    fun waitForContinue(message: String = "Press Enter to continue") {
        println("💬 $message...")
        scanner.nextLine()
    }
    
    /**
     * Display a formatted message with options for user action
     */
    fun displayActionPrompt(
        title: String,
        message: String,
        actions: List<UserAction>
    ): UserAction {
        println("\n" + "=".repeat(60))
        println("📋 $title")
        println("=".repeat(60))
        println(message)
        println()

        val actionTexts = actions.map { it.description }
        val selectedText = promptChoice("What would you like to do?", actionTexts)

        return actions.first { it.description == selectedText }
    }
}

/**
 * Represents a user action option
 */
data class UserAction(
    val id: String,
    val description: String,
    val action: () -> Unit = {}
) {
    companion object {
        fun continueAction(description: String = "Continue with current plan") = UserAction("continue", description)
        fun modify(description: String = "Modify the approach") = UserAction("modify", description)
        fun cancel(description: String = "Cancel and exit") = UserAction("cancel", description)
        fun retry(description: String = "Retry the operation") = UserAction("retry", description)
        fun custom(id: String, description: String, action: () -> Unit = {}) = UserAction(id, description, action)
    }
}

/**
 * Result of user interaction
 */
sealed class UserInteractionResult {
    object Continue : UserInteractionResult()
    object Cancel : UserInteractionResult()
    object Retry : UserInteractionResult()
    data class Custom(val actionId: String, val data: Map<String, String> = emptyMap()) : UserInteractionResult()
    data class Input(val value: String) : UserInteractionResult()
    data class Choice(val selectedOption: String, val selectedIndex: Int) : UserInteractionResult()
}
