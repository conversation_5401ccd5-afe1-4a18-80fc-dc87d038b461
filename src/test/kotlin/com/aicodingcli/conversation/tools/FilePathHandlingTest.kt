package com.aicodingcli.conversation.tools

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach
import kotlinx.coroutines.runBlocking
import java.io.File
import java.nio.file.Files
import java.nio.file.Path

class FilePathHandlingTest {

    private lateinit var tempDir: Path
    private lateinit var workingDirectory: String

    @BeforeEach
    fun setUp() {
        tempDir = Files.createTempDirectory("file-path-test")
        workingDirectory = tempDir.toString()
    }

    @AfterEach
    fun tearDown() {
        tempDir.toFile().deleteRecursively()
    }

    @Test
    fun `should handle relative paths with forward slashes correctly`() = runBlocking {
        val handler = SaveFileHandler()
        val relativePath = "src/main/kotlin/TestClass.kt"
        val content = "class TestClass"

        val result = handler.execute(
            parameters = mapOf(
                "path" to relativePath,
                "file_content" to content
            ),
            workingDirectory = workingDirectory
        )

        assertTrue(result.success, "Should successfully create file with relative path containing forward slashes")
        
        val expectedFile = File(workingDirectory, relativePath)
        assertTrue(expectedFile.exists(), "File should exist at the expected location")
        assertEquals(content, expectedFile.readText(), "File content should match")
    }

    @Test
    fun `should handle nested directory paths correctly`() = runBlocking {
        val handler = SaveFileHandler()
        val nestedPath = "com/example/calculator/Calculator.kt"
        val content = "package com.example.calculator\n\nclass Calculator"

        val result = handler.execute(
            parameters = mapOf(
                "path" to nestedPath,
                "file_content" to content
            ),
            workingDirectory = workingDirectory
        )

        assertTrue(result.success, "Should successfully create file in nested directories")
        
        val expectedFile = File(workingDirectory, nestedPath)
        assertTrue(expectedFile.exists(), "File should exist in nested directory")
        assertEquals(content, expectedFile.readText(), "File content should match")
    }

    @Test
    fun `should handle absolute paths correctly`() = runBlocking {
        val handler = SaveFileHandler()
        val absolutePath = File(tempDir.toFile(), "absolute-test.kt").absolutePath
        val content = "// Absolute path test"

        val result = handler.execute(
            parameters = mapOf(
                "path" to absolutePath,
                "file_content" to content
            ),
            workingDirectory = workingDirectory
        )

        assertTrue(result.success, "Should successfully create file with absolute path")
        
        val expectedFile = File(absolutePath)
        assertTrue(expectedFile.exists(), "File should exist at absolute path")
        assertEquals(content, expectedFile.readText(), "File content should match")
    }

    @Test
    fun `view handler should handle relative paths with forward slashes`() = runBlocking {
        // First create a file
        val relativePath = "src/test/Example.kt"
        val content = "class Example"
        val file = File(workingDirectory, relativePath)
        file.parentFile.mkdirs()
        file.writeText(content)

        val handler = ViewHandler()
        val result = handler.execute(
            parameters = mapOf("path" to relativePath),
            workingDirectory = workingDirectory
        )

        assertTrue(result.success, "Should successfully view file with relative path")
        assertTrue(result.output.contains(content), "Should return file content")
    }

    @Test
    fun `str-replace-editor should handle relative paths with forward slashes`() = runBlocking {
        // First create a file
        val relativePath = "src/main/Test.kt"
        val originalContent = "class Test { fun old() {} }"
        val file = File(workingDirectory, relativePath)
        file.parentFile.mkdirs()
        file.writeText(originalContent)

        val handler = StrReplaceEditorHandler()
        val result = handler.execute(
            parameters = mapOf(
                "path" to relativePath,
                "old_str" to "old()",
                "new_str" to "new()"
            ),
            workingDirectory = workingDirectory
        )

        assertTrue(result.success, "Should successfully replace text in file with relative path")
        
        val updatedContent = file.readText()
        assertTrue(updatedContent.contains("new()"), "File should contain replaced text")
        assertFalse(updatedContent.contains("old()"), "File should not contain old text")
    }

    @Test
    fun `remove-files should handle relative paths with forward slashes`() = runBlocking {
        // First create files
        val relativePath1 = "src/temp/File1.kt"
        val relativePath2 = "src/temp/File2.kt"
        
        val file1 = File(workingDirectory, relativePath1)
        val file2 = File(workingDirectory, relativePath2)
        
        file1.parentFile.mkdirs()
        file1.writeText("content1")
        file2.writeText("content2")

        val handler = RemoveFilesHandler()
        val result = handler.execute(
            parameters = mapOf("file_paths" to "$relativePath1, $relativePath2"),
            workingDirectory = workingDirectory
        )

        assertTrue(result.success, "Should successfully remove files with relative paths")
        assertFalse(file1.exists(), "File1 should be removed")
        assertFalse(file2.exists(), "File2 should be removed")
    }

    @Test
    fun `should reject paths with null characters`() = runBlocking {
        val handler = SaveFileHandler()
        val invalidPath = "test\u0000file.kt"

        val result = handler.execute(
            parameters = mapOf(
                "path" to invalidPath,
                "file_content" to "content"
            ),
            workingDirectory = workingDirectory
        )

        assertFalse(result.success, "Should reject paths with null characters")
        assertTrue(result.error?.contains("Invalid file path") == true, "Should provide appropriate error message")
    }
}
