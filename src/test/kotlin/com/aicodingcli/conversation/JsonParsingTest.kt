package com.aicodingcli.conversation

import kotlinx.serialization.json.Json
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

@kotlinx.serialization.Serializable
data class TestActionDecisionResponseJson(
    val action: String,
    val toolName: String? = null,
    val parameters: Map<String, JsonElement>? = null,
    val reasoning: String,
    val confidence: Double
)

class JsonParsingTest {

    private val json = Json { ignoreUnknownKeys = true }

    @Test
    fun `should parse JSON with boolean false correctly`() {
        val jsonString = """
        {
          "action": "EXECUTE_TOOL",
          "toolName": "read-terminal",
          "parameters": {
            "only_selected": false
          },
          "reasoning": "Run tests to verify that they pass.",
          "confidence": 0.9
        }
        """.trimIndent()

        // This should not throw an exception
        val result = json.decodeFromString<TestActionDecisionResponseJson>(jsonString)

        assertEquals("EXECUTE_TOOL", result.action)
        assertEquals("read-terminal", result.toolName)
        assertNotNull(result.parameters)
        assertEquals(JsonPrimitive(false), result.parameters!!["only_selected"])
    }

    @Test
    fun `should parse JSON with boolean true correctly`() {
        val jsonString = """
        {
          "action": "EXECUTE_TOOL",
          "toolName": "read-terminal",
          "parameters": {
            "only_selected": true
          },
          "reasoning": "Read only selected text.",
          "confidence": 0.9
        }
        """.trimIndent()

        // This should not throw an exception
        val result = json.decodeFromString<TestActionDecisionResponseJson>(jsonString)

        assertEquals("EXECUTE_TOOL", result.action)
        assertEquals("read-terminal", result.toolName)
        assertNotNull(result.parameters)
        assertEquals(JsonPrimitive(true), result.parameters!!["only_selected"])
    }

    @Test
    fun `should parse JSON with numeric values correctly`() {
        val jsonString = """
        {
          "action": "EXECUTE_TOOL",
          "toolName": "test-tool",
          "parameters": {
            "count": 42,
            "ratio": 3.14
          },
          "reasoning": "Test numeric values.",
          "confidence": 0.95
        }
        """.trimIndent()

        // This should not throw an exception
        val result = json.decodeFromString<TestActionDecisionResponseJson>(jsonString)

        assertEquals("EXECUTE_TOOL", result.action)
        assertEquals("test-tool", result.toolName)
        assertNotNull(result.parameters)
        assertEquals(JsonPrimitive(42), result.parameters!!["count"])
        assertEquals(JsonPrimitive(3.14), result.parameters!!["ratio"])
    }
}
